// Simple test to verify role validation fixes
const { z } = require('zod');

// Recreate the schemas to test them
const StatusSchema = z.enum([
  "active",
  "inactive",
  "submitted",
  "received", 
  "negotiating",
  "agreed",
  "created",
  "inprogress",
  "reviewing",
  "completed",
  "closed",
  "terminated"
]);

const RequiredStringSchema = z.string().min(1, "This field is required");

const PermissionActionSchema = z.enum([
  "create",
  "read",
  "update",
  "delete",
]);

const EntityPermissionsSchema = z.record(
  z.string(), // entity name (e.g., 'user', 'role', 'document')
  z.array(PermissionActionSchema) // array of actions for that entity
);

const CreateRoleSchema = z.object({
  name: RequiredStringSchema,
  description: z.string().optional(),
  status: StatusSchema.default("created"),
  permissions: EntityPermissionsSchema.default({}),
});

// Test cases
console.log('Testing role validation fixes...\n');

// Test 1: Basic role creation with minimal data
try {
  const result1 = CreateRoleSchema.parse({
    name: "Test Role"
  });
  console.log('✅ Test 1 PASSED: Basic role creation');
  console.log('   Result:', JSON.stringify(result1, null, 2));
} catch (error) {
  console.log('❌ Test 1 FAILED: Basic role creation');
  console.log('   Error:', error.message);
}

// Test 2: Role creation with all status values from Prisma schema
const statusesToTest = ["active", "inactive", "created", "closed", "terminated"];
statusesToTest.forEach((status, index) => {
  try {
    const result = CreateRoleSchema.parse({
      name: `Test Role ${index + 2}`,
      status: status
    });
    console.log(`✅ Test ${index + 2} PASSED: Status "${status}" validation`);
  } catch (error) {
    console.log(`❌ Test ${index + 2} FAILED: Status "${status}" validation`);
    console.log('   Error:', error.message);
  }
});

// Test 3: Role creation with proper permissions structure
try {
  const result7 = CreateRoleSchema.parse({
    name: "Test Role 7",
    description: "Test role with permissions",
    permissions: {
      user: ["create", "read", "update"],
      role: ["read"],
      document: ["create", "read", "update", "delete"]
    }
  });
  console.log('✅ Test 7 PASSED: Role with proper permissions structure');
  console.log('   Permissions:', JSON.stringify(result7.permissions, null, 2));
} catch (error) {
  console.log('❌ Test 7 FAILED: Role with proper permissions structure');
  console.log('   Error:', error.message);
}

// Test 4: Role creation with empty permissions (should default to {})
try {
  const result8 = CreateRoleSchema.parse({
    name: "Test Role 8"
  });
  console.log('✅ Test 8 PASSED: Default empty permissions');
  console.log('   Default permissions:', JSON.stringify(result8.permissions, null, 2));
} catch (error) {
  console.log('❌ Test 8 FAILED: Default empty permissions');
  console.log('   Error:', error.message);
}

// Test 5: Invalid permissions structure (should fail)
try {
  const result9 = CreateRoleSchema.parse({
    name: "Test Role 9",
    permissions: ["create", "read"] // This should fail - wrong structure
  });
  console.log('❌ Test 9 FAILED: Should have rejected invalid permissions structure');
} catch (error) {
  console.log('✅ Test 9 PASSED: Correctly rejected invalid permissions structure');
  console.log('   Error:', error.message);
}

console.log('\nValidation tests completed!');

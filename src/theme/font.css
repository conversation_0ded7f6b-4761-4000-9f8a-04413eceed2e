@font-face {
    font-family: "Manrope";
    src: url('/Manrope/Manrope-VariableFont_wght.ttf') format('truetype');
  }

  
h1 {
    font-size: 5.375em !important;
}

h2 {
    font-size: 4.188em !important;
}

h3 {
    font-size: 3.438em !important;
}

h4 {
    font-size: 2.75em !important;
}

h5 {
    font-size: 2.188em !important;
}

h6 {
    font-size: 1.75em !important;
}

caption {
    font-size: .75em !important;
}

small {
    font-size: .625em !important;
}

a {
    font-size: 1.2em !important;
}
  
h1, h2, h3, h4 {
    font-weight: 500;
}

h5, h6 {
    font-weight: 300;
}

h1, h2, h3, h4, h5, h6, p, caption {
    display: inline-block !important;
    word-wrap: break-word;
    margin: 0;
    padding: 0;
}

/* Paragraph one */
p {
    text-wrap: wrap;
}

p.regular-para1 {
    font-size: 1.7em !important;
    font-weight: 250 !important;
}

p.medium-para1, p.bold-para1 {
    font-size: 1em !important;
}

p.medium-para1 {
    font-weight: 700 !important;
}

p.bold-para1 {
    font-weight: bold !important;
}

/* Paragraph two */

p.regular-para2 {
    font-size: 1.21em;
    font-weight: 200;
}

p.medium-para2 , p.bold-para2 {
    font-size: 1em;
}

p.medium-para2 {
    font-weight: 700;
}

p.bold-para2 {
    font-weight: bold;
}

/* Paragraph three */

p.regular-para3, p.medium-para3, p.bold-para3 {
    font-size: .875em !important;
}

p.regular-para3 {
    font-weight: 500 !important;
}

p.medium-para3 {
    font-weight: 700 !important;
}

p.bold-para3 {
    font-weight: bold !important;
}

.navbar-caption {
    display: inline-block;
}

.footer-email {
    font-size: 3em !important;
}


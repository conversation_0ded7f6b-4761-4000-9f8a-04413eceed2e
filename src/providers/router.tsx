"use client";

import { Fragment, useEffect } from "react";
import { usePathname } from "next/navigation";
import { useAuth } from "@/hooks/useAuth";

const DashboardRouteValidator = () => {
  const path = usePathname();
  const { isCurrentRouteDashboardView, isDashboardView, isAuthenticated } =
    useAuth();

  useEffect(() => {
    isCurrentRouteDashboardView();
  }, [path, isAuthenticated, isDashboardView]);

  return null;
};

export const RouteManager = ({ children }: { children: React.ReactNode }) => {
  return (
    <Fragment>
      {children}
      <DashboardRouteValidator />
    </Fragment>
  );
};

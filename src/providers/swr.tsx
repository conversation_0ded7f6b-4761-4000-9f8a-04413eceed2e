import { SWRConfig, SWRConfiguration } from "swr";

export function SWRProvider({ children }: { children: React.ReactNode }) {
  const options: SWRConfiguration = {
    refreshInterval: 3000,
    revalidateIfStale: true,
    revalidateOnFocus: true,
    revalidateOnReconnect: true,
    fetcher: (resource, init) =>
      fetch(resource, init).then((res) => res.json()),
  };

  return <SWRConfig value={options}>{children}</SWRConfig>;
}

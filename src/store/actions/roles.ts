import { createAsyncThunk } from "@reduxjs/toolkit";
import { api } from "@/lib/common/requests";
import { toast } from "sonner";
import permissions from "./permissions.json";

// Role interface for role-specific operations
export interface Role {
  id: string;
  name: string;
  description?: string;
  status: "active" | "inactive" | "created";
  permissions: Record<string, string[]>; // JSON-based structure: { [entity]: [actions] }
  createdAt: Date;
  updatedAt?: Date;
}

export interface RoleState {
  roles: Role[];
  isLoading: boolean;
  error?: string;
}

// Fetch all roles
export const fetchRoles = createAsyncThunk(
  "roles/fetchRoles",
  async (
    params: {
      limit?: number;
      offset?: number;
      search?: string;
      status?: string;
    } = {},
    { rejectWithValue }
  ) => {
    try {
      const queryParams = new URLSearchParams();
      if (params?.limit) queryParams.append("limit", params.limit.toString());
      if (params?.offset)
        queryParams.append("offset", params.offset.toString());
      if (params?.search) queryParams.append("search", params.search);
      if (params?.status) queryParams.append("status", params.status);

      const result = await api.get(
        `roles${queryParams.toString() ? `?${queryParams.toString()}` : ""}`
      );
      if (result.success) {
        return result.data;
      }
      return rejectWithValue(result.message || "Failed to fetch roles");
    } catch (error) {
      const errorMessage =
        error instanceof Error ? error.message : "Failed to fetch roles";
      return rejectWithValue(errorMessage);
    }
  }
);

// Fetch single role by ID
export const fetchRoleById = createAsyncThunk(
  "roles/fetchRoleById",
  async (roleId: string, { rejectWithValue }) => {
    try {
      const result = await api.get(`roles/${roleId}`);
      if (result.success) {
        return result.data;
      }
      return rejectWithValue(result.message || "Failed to fetch role");
    } catch (error) {
      const errorMessage =
        error instanceof Error ? error.message : "Failed to fetch role";
      return rejectWithValue(errorMessage);
    }
  }
);

// Create new role
export const createRole = createAsyncThunk(
  "roles/createRole",
  async (
    roleData: {
      name: string;
      description?: string;
      permissions?: Record<string, string[]>;
      status?: string;
    },
    { rejectWithValue }
  ) => {
    try {
      const result = await api.post("roles", roleData);
      if (result.success) {
        toast.success("Role created successfully");
        return result.data;
      }
      toast.error(result.message || "Failed to create role");
      return rejectWithValue(result.message || "Failed to create role");
    } catch (error) {
      const errorMessage =
        error instanceof Error ? error.message : "Failed to create role";
      toast.error(errorMessage);
      return rejectWithValue(errorMessage);
    }
  }
);

// Update role
export const updateRole = createAsyncThunk(
  "roles/updateRole",
  async (
    { roleId, roleData }: { roleId: string; roleData: Partial<Role> },
    { rejectWithValue }
  ) => {
    try {
      const result = await api.put(`roles`, {
        ...roleData,
        id: roleId,
      });
      if (result.success) {
        toast.success("Role updated successfully");
        return result.data;
      }
      toast.error(result.message || "Failed to update role");
      return rejectWithValue(result.message || "Failed to update role");
    } catch (error) {
      const errorMessage =
        error instanceof Error ? error.message : "Failed to update role";
      toast.error(errorMessage);
      return rejectWithValue(errorMessage);
    }
  }
);

// Delete role
export const deleteRole = createAsyncThunk(
  "roles/deleteRole",
  async (roleId: string, { rejectWithValue }) => {
    try {
      const result = await api.delete(`roles`, { id: roleId });
      if (result.success) {
        toast.success("Role deleted successfully");
        return roleId;
      }
      toast.error(result.message || "Failed to delete role");
      return rejectWithValue(result.message || "Failed to delete role");
    } catch (error) {
      const errorMessage =
        error instanceof Error ? error.message : "Failed to delete role";
      toast.error(errorMessage);
      return rejectWithValue(errorMessage);
    }
  }
);

// Update role permissions
export const updateRolePermissions = createAsyncThunk(
  "roles/updateRolePermissions",
  async (
    {
      roleId,
      permissions,
    }: { roleId: string; permissions: Record<string, string[]> },
    { rejectWithValue }
  ) => {
    try {
      const result = await api.put("roles/permissions", {
        id: roleId,
        permissions,
      });
      if (result.success) {
        toast.success("Role permissions updated successfully");
        return result.data;
      }
      toast.error(result.message || "Failed to update role permissions");
      return rejectWithValue(
        result.message || "Failed to update role permissions"
      );
    } catch (error) {
      const errorMessage =
        error instanceof Error
          ? error.message
          : "Failed to update role permissions";
      toast.error(errorMessage);
      return rejectWithValue(errorMessage);
    }
  }
);

// Get available permissions (system-wide)
export const fetchAvailablePermissions = createAsyncThunk(
  "roles/fetchAvailablePermissions",
  async (_, { rejectWithValue }) => {
    try {
      // This could be a separate endpoint or hardcoded list
      return permissions;
    } catch (error) {
      const errorMessage =
        error instanceof Error ? error.message : "Failed to fetch permissions";
      return rejectWithValue(errorMessage);
    }
  }
);

// Bulk operations
export const bulkDeleteRoles = createAsyncThunk(
  "roles/bulkDeleteRoles",
  async (roleIds: string[], { rejectWithValue }) => {
    try {
      const results = await Promise.all(
        roleIds.map((id) => api.delete(`roles`, { id }))
      );

      const failed = results.filter((result) => !result.success);
      if (failed.length > 0) {
        toast.error(`Failed to delete ${failed.length} roles`);
        return rejectWithValue(`Failed to delete ${failed.length} roles`);
      }

      toast.success(`Successfully deleted ${roleIds.length} roles`);
      return roleIds;
    } catch (error) {
      const errorMessage =
        error instanceof Error ? error.message : "Failed to bulk delete roles";
      toast.error(errorMessage);
      return rejectWithValue(errorMessage);
    }
  }
);

// Update role status
export const updateRoleStatus = createAsyncThunk(
  "roles/updateRoleStatus",
  async (
    { roleId, status }: { roleId: string; status: "active" | "inactive" },
    { rejectWithValue }
  ) => {
    try {
      const result = await api.put(`roles`, { id: roleId, status });
      if (result.success) {
        toast.success(
          `Role ${
            status === "active" ? "activated" : "deactivated"
          } successfully`
        );
        return result.data;
      }
      toast.error(result.message || "Failed to update role status");
      return rejectWithValue(result.message || "Failed to update role status");
    } catch (error) {
      const errorMessage =
        error instanceof Error ? error.message : "Failed to update role status";
      toast.error(errorMessage);
      return rejectWithValue(errorMessage);
    }
  }
);

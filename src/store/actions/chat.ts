import { createAsyncThunk } from "@reduxjs/toolkit";
import { toast } from "sonner";
import { ChatService } from "@/lib/api/services/chat";
import type {
  CreateRoom,
  CreateMessage,
  UpdateRoom,
  UpdateMessage,
  UserState,
} from "@/lib/api/validators/schemas/chat";

// Initialize chat service
const chatService = new ChatService();

// Fetch all rooms for a user
export const fetchRooms = createAsyncThunk(
  "chat/fetchRooms",
  async (userId: string | undefined, { rejectWithValue }) => {
    try {
      const response = await chatService.getRooms(userId);

      if (response.success && !response.error) {
        return response.data;
      } else {
        toast.error(response.message || "Failed to fetch chat rooms");
        return rejectWithValue({
          success: false,
          error: true,
          message: response.message || "Failed to fetch chat rooms",
        });
      }
    } catch (error) {
      const errorMessage =
        error instanceof Error ? error.message : "Failed to fetch chat rooms";
      toast.error(errorMessage);
      return rejectWithValue({
        success: false,
        error: true,
        message: errorMessage,
      });
    }
  }
);

// Fetch messages for a room
export const fetchMessages = createAsyncThunk(
  "chat/fetchMessages",
  async (
    {
      roomId,
      limit = 50,
      offset = 0,
    }: { roomId: string; limit?: number; offset?: number },
    { rejectWithValue }
  ) => {
    try {
      const response = await chatService.getMessages(roomId, limit, offset);

      if (response.success && !response.error) {
        return { roomId, messages: response.data };
      } else {
        toast.error(response.message || "Failed to fetch messages");
        return rejectWithValue({
          success: false,
          error: true,
          message: response.message || "Failed to fetch messages",
        });
      }
    } catch (error) {
      const errorMessage =
        error instanceof Error ? error.message : "Failed to fetch messages";
      toast.error(errorMessage);
      return rejectWithValue({
        success: false,
        error: true,
        message: errorMessage,
      });
    }
  }
);

// Send a message
export const sendMessage = createAsyncThunk(
  "chat/sendMessage",
  async (messageData: CreateMessage, { rejectWithValue }) => {
    try {
      const response = await chatService.sendMessage(messageData);

      if (response.success && !response.error) {
        // Don't show toast for successful message sending to avoid spam
        return response.data;
      } else {
        toast.error(response.message || "Failed to send message");
        return rejectWithValue({
          success: false,
          error: true,
          message: response.message || "Failed to send message",
        });
      }
    } catch (error) {
      const errorMessage =
        error instanceof Error ? error.message : "Failed to send message";
      toast.error(errorMessage);
      return rejectWithValue({
        success: false,
        error: true,
        message: errorMessage,
      });
    }
  }
);

// Create a new room
export const createRoom = createAsyncThunk(
  "chat/createRoom",
  async (roomData: CreateRoom, { rejectWithValue }) => {
    try {
      const response = await chatService.createRoom(roomData);

      if (response.success && !response.error) {
        toast.success(response.message || "Room created successfully");
        return response.data;
      } else {
        toast.error(response.message || "Failed to create room");
        return rejectWithValue({
          success: false,
          error: true,
          message: response.message || "Failed to create room",
        });
      }
    } catch (error) {
      const errorMessage =
        error instanceof Error ? error.message : "Failed to create room";
      toast.error(errorMessage);
      return rejectWithValue({
        success: false,
        error: true,
        message: errorMessage,
      });
    }
  }
);

// Update user state (online/offline/typing/away)
export const updateUserState = createAsyncThunk(
  "chat/updateUserState",
  async (
    {
      userId,
      state,
      roomId,
    }: { userId: string; state: UserState; roomId?: string },
    { rejectWithValue }
  ) => {
    try {
      const response = await chatService.updateUserState(userId, state, roomId);

      if (response.success && !response.error) {
        return response.data;
      } else {
        // Don't show toast for user state updates to avoid spam
        return rejectWithValue({
          success: false,
          error: true,
          message: response.message || "Failed to update user state",
        });
      }
    } catch (error) {
      const errorMessage =
        error instanceof Error ? error.message : "Failed to update user state";
      return rejectWithValue({
        success: false,
        error: true,
        message: errorMessage,
      });
    }
  }
);

// Get typing users for a room
export const fetchTypingUsers = createAsyncThunk(
  "chat/fetchTypingUsers",
  async (roomId: string, { rejectWithValue }) => {
    try {
      const response = await chatService.getTypingUsers(roomId);

      if (response.success && !response.error) {
        return { roomId, typingUsers: response.data };
      } else {
        return rejectWithValue({
          success: false,
          error: true,
          message: response.message || "Failed to fetch typing users",
        });
      }
    } catch (error) {
      const errorMessage =
        error instanceof Error ? error.message : "Failed to fetch typing users";
      return rejectWithValue({
        success: false,
        error: true,
        message: errorMessage,
      });
    }
  }
);

// Fetch chat statistics
export const fetchChatStatistics = createAsyncThunk(
  "chat/fetchStatistics",
  async (_, { rejectWithValue }) => {
    try {
      const response = await chatService.getStatistics();

      if (response.success && !response.error) {
        return response.data;
      } else {
        toast.error(response.message || "Failed to fetch chat statistics");
        return rejectWithValue({
          success: false,
          error: true,
          message: response.message || "Failed to fetch chat statistics",
        });
      }
    } catch (error) {
      const errorMessage =
        error instanceof Error
          ? error.message
          : "Failed to fetch chat statistics";
      toast.error(errorMessage);
      return rejectWithValue({
        success: false,
        error: true,
        message: errorMessage,
      });
    }
  }
);

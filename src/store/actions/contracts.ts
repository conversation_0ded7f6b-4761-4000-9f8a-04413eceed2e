import { createAsyncThunk } from "@reduxjs/toolkit";
import { ContractService } from "@/lib/api/services/contract";
import type {
  Contract,
  CreateContract,
  UpdateContract,
  ContractStatistics,
} from "@/lib/api/validators/schemas/contract";
import { toast } from "sonner";

const contractService = new ContractService();

// Fetch all contracts
export const fetchContracts = createAsyncThunk(
  "contracts/fetchContracts",
  async (_, { rejectWithValue }) => {
    try {
      const response = await contractService.getContracts();
      
      if (response.success && !response.error) {
        return response.data;
      } else {
        toast.error(response.message || "Failed to fetch contracts");
        return rejectWithValue({
          success: false,
          error: true,
          message: response.message || "Failed to fetch contracts",
        });
      }
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : "Failed to fetch contracts";
      toast.error(errorMessage);
      return rejectWithValue({
        success: false,
        error: true,
        message: errorMessage,
      });
    }
  }
);

// Fetch single contract
export const fetchContract = createAsyncThunk(
  "contracts/fetchContract",
  async (contractId: string, { rejectWithValue }) => {
    try {
      const response = await contractService.getContract(contractId);
      
      if (response.success && !response.error) {
        return response.data;
      } else {
        toast.error(response.message || "Failed to fetch contract");
        return rejectWithValue({
          success: false,
          error: true,
          message: response.message || "Failed to fetch contract",
        });
      }
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : "Failed to fetch contract";
      toast.error(errorMessage);
      return rejectWithValue({
        success: false,
        error: true,
        message: errorMessage,
      });
    }
  }
);

// Create contract
export const createContract = createAsyncThunk(
  "contracts/createContract",
  async (contractData: CreateContract, { rejectWithValue }) => {
    try {
      const response = await contractService.createContract(contractData);
      
      if (response.success && !response.error) {
        toast.success(response.message || "Contract created successfully");
        return response.data;
      } else {
        toast.error(response.message || "Failed to create contract");
        return rejectWithValue({
          success: false,
          error: true,
          message: response.message || "Failed to create contract",
        });
      }
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : "Failed to create contract";
      toast.error(errorMessage);
      return rejectWithValue({
        success: false,
        error: true,
        message: errorMessage,
      });
    }
  }
);

// Update contract
export const updateContract = createAsyncThunk(
  "contracts/updateContract",
  async (
    { id, data }: { id: string; data: Partial<UpdateContract> },
    { rejectWithValue }
  ) => {
    try {
      const response = await contractService.updateContract(id, data);
      
      if (response.success && !response.error) {
        toast.success(response.message || "Contract updated successfully");
        return response.data;
      } else {
        toast.error(response.message || "Failed to update contract");
        return rejectWithValue({
          success: false,
          error: true,
          message: response.message || "Failed to update contract",
        });
      }
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : "Failed to update contract";
      toast.error(errorMessage);
      return rejectWithValue({
        success: false,
        error: true,
        message: errorMessage,
      });
    }
  }
);

// Delete contract
export const deleteContract = createAsyncThunk(
  "contracts/deleteContract",
  async (contractId: string, { rejectWithValue }) => {
    try {
      const response = await contractService.deleteContract(contractId);
      
      if (response.success && !response.error) {
        toast.success(response.message || "Contract deleted successfully");
        return contractId;
      } else {
        toast.error(response.message || "Failed to delete contract");
        return rejectWithValue({
          success: false,
          error: true,
          message: response.message || "Failed to delete contract",
        });
      }
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : "Failed to delete contract";
      toast.error(errorMessage);
      return rejectWithValue({
        success: false,
        error: true,
        message: errorMessage,
      });
    }
  }
);

// Fetch contract statistics
export const fetchContractStatistics = createAsyncThunk(
  "contracts/fetchStatistics",
  async (_, { rejectWithValue }) => {
    try {
      const response = await contractService.getContractStatistics();
      
      if (response.success && !response.error) {
        return response.data;
      } else {
        toast.error(response.message || "Failed to fetch contract statistics");
        return rejectWithValue({
          success: false,
          error: true,
          message: response.message || "Failed to fetch contract statistics",
        });
      }
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : "Failed to fetch contract statistics";
      toast.error(errorMessage);
      return rejectWithValue({
        success: false,
        error: true,
        message: errorMessage,
      });
    }
  }
);

import { createSlice, PayloadAction } from "@reduxjs/toolkit";
import {
  fetchRoles,
  fetchRoleById,
  createRole,
  updateRole,
  deleteRole,
  updateRolePermissions,
  fetchAvailablePermissions,
  bulkDeleteRoles,
  updateRoleStatus,
  Role,
  RoleState,
} from "../actions/roles";

const initialState: RoleState = {
  roles: [],
  isLoading: false,
  error: undefined,
};

const rolesSlice = createSlice({
  name: "roles",
  initialState,
  reducers: {
    // Clear error
    clearError: (state) => {
      state.error = undefined;
    },

    // Set loading state
    setLoading: (state, action: PayloadAction<boolean>) => {
      state.isLoading = action.payload;
    },

    // Clear all roles
    clearRoles: (state) => {
      state.roles = [];
    },

    // Update role locally (optimistic update)
    updateRoleLocal: (state, action: PayloadAction<Role>) => {
      const index = state.roles.findIndex(
        (role) => role.id === action.payload.id
      );
      if (index !== -1) {
        state.roles[index] = action.payload;
      }
    },

    // Remove role locally (optimistic delete)
    removeRoleLocal: (state, action: PayloadAction<string>) => {
      state.roles = state.roles.filter((role) => role.id !== action.payload);
    },
  },
  extraReducers: (builder) => {
    // Fetch roles
    builder
      .addCase(fetchRoles.pending, (state) => {
        state.isLoading = true;
        state.error = undefined;
      })
      .addCase(fetchRoles.fulfilled, (state, action) => {
        state.isLoading = false;
        // Handle both array response and paginated response
        if (Array.isArray(action.payload)) {
          state.roles = action.payload;
        } else if (action.payload?.roles) {
          state.roles = action.payload.roles;
        } else {
          state.roles = [];
        }
      })
      .addCase(fetchRoles.rejected, (state, action) => {
        state.isLoading = false;
        state.error = action.payload as string;
      });

    // Fetch role by ID
    builder
      .addCase(fetchRoleById.pending, (state) => {
        state.isLoading = true;
        state.error = undefined;
      })
      .addCase(fetchRoleById.fulfilled, (state, action) => {
        state.isLoading = false;
        // Update or add the role to the list
        const existingIndex = state.roles.findIndex(
          (role) => role.id === action.payload.id
        );
        if (existingIndex !== -1) {
          state.roles[existingIndex] = action.payload;
        } else {
          state.roles.push(action.payload);
        }
      })
      .addCase(fetchRoleById.rejected, (state, action) => {
        state.isLoading = false;
        state.error = action.payload as string;
      });

    // Create role
    builder
      .addCase(createRole.pending, (state) => {
        state.isLoading = true;
        state.error = undefined;
      })
      .addCase(createRole.fulfilled, (state, action) => {
        state.isLoading = false;
        state.roles.push(action.payload);
      })
      .addCase(createRole.rejected, (state, action) => {
        state.isLoading = false;
        state.error = action.payload as string;
      });

    // Update role
    builder
      .addCase(updateRole.pending, (state) => {
        state.isLoading = true;
        state.error = undefined;
      })
      .addCase(updateRole.fulfilled, (state, action) => {
        state.isLoading = false;
        const index = state.roles.findIndex(
          (role) => role.id === action.payload.id
        );
        if (index !== -1) {
          state.roles[index] = action.payload;
        }
      })
      .addCase(updateRole.rejected, (state, action) => {
        state.isLoading = false;
        state.error = action.payload as string;
      });

    // Delete role
    builder
      .addCase(deleteRole.pending, (state) => {
        state.isLoading = true;
        state.error = undefined;
      })
      .addCase(deleteRole.fulfilled, (state, action) => {
        state.isLoading = false;
        state.roles = state.roles.filter((role) => role.id !== action.payload);
      })
      .addCase(deleteRole.rejected, (state, action) => {
        state.isLoading = false;
        state.error = action.payload as string;
      });

    // Update role permissions
    builder
      .addCase(updateRolePermissions.pending, (state) => {
        state.isLoading = true;
        state.error = undefined;
      })
      .addCase(updateRolePermissions.fulfilled, (state, action) => {
        state.isLoading = false;
        const index = state.roles.findIndex(
          (role) => role.id === action.payload.id
        );
        if (index !== -1) {
          state.roles[index] = action.payload;
        }
      })
      .addCase(updateRolePermissions.rejected, (state, action) => {
        state.isLoading = false;
        state.error = action.payload as string;
      });

    // Bulk delete roles
    builder
      .addCase(bulkDeleteRoles.pending, (state) => {
        state.isLoading = true;
        state.error = undefined;
      })
      .addCase(bulkDeleteRoles.fulfilled, (state, action) => {
        state.isLoading = false;
        state.roles = state.roles.filter(
          (role) => !action.payload.includes(role.id)
        );
      })
      .addCase(bulkDeleteRoles.rejected, (state, action) => {
        state.isLoading = false;
        state.error = action.payload as string;
      });

    // Update role status
    builder
      .addCase(updateRoleStatus.pending, (state) => {
        state.isLoading = true;
        state.error = undefined;
      })
      .addCase(updateRoleStatus.fulfilled, (state, action) => {
        state.isLoading = false;
        const index = state.roles.findIndex(
          (role) => role.id === action.payload.id
        );
        if (index !== -1) {
          state.roles[index] = action.payload;
        }
      })
      .addCase(updateRoleStatus.rejected, (state, action) => {
        state.isLoading = false;
        state.error = action.payload as string;
      });

    // Fetch available permissions (no state change needed, just for caching)
    builder
      .addCase(fetchAvailablePermissions.pending, (state) => {
        state.isLoading = true;
        state.error = undefined;
      })
      .addCase(fetchAvailablePermissions.fulfilled, (state) => {
        state.isLoading = false;
        // Permissions are typically used directly, not stored in state
      })
      .addCase(fetchAvailablePermissions.rejected, (state, action) => {
        state.isLoading = false;
        state.error = action.payload as string;
      });
  },
});

// Export actions
export const {
  clearError,
  setLoading,
  clearRoles,
  updateRoleLocal,
  removeRoleLocal,
} = rolesSlice.actions;

// Export selectors
export const selectRoles = (state: { roles: RoleState }) => state.roles.roles;
export const selectRolesLoading = (state: { roles: RoleState }) =>
  state.roles.isLoading;
export const selectRolesError = (state: { roles: RoleState }) =>
  state.roles.error;
export const selectRoleById = (state: { roles: RoleState }, roleId: string) =>
  state.roles.roles.find((role) => role.id === roleId);

// Export reducer
export default rolesSlice.reducer;

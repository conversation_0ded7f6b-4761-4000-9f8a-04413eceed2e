import { createSlice, PayloadAction } from "@reduxjs/toolkit";
import { RootState } from "../index";
import {
  fetchUsers,
  updateUserProfile,
  assignRoleToUser,
  removeRoleFromUser,
  fetchAvailablePermissions,
  User,
  Role,
  RBACState,
} from "../actions/rbac";

const initialState: RBACState = {
  users: [],
  currentUser: undefined,
  isLoading: false,
  error: undefined,
};

const rbacSlice = createSlice({
  name: "rbac",
  initialState,
  reducers: {
    setLoading: (state, action: PayloadAction<boolean>) => {
      state.isLoading = action.payload;
    },
    setError: (state, action: PayloadAction<string | undefined>) => {
      state.error = action.payload;
    },
    clearError: (state) => {
      state.error = undefined;
    },
    updateCurrentUser: (state, action: PayloadAction<Partial<User>>) => {
      if (state.currentUser) {
        state.currentUser = { ...state.currentUser, ...action.payload };
      }
    },
  },
  extraReducers: (builder) => {
    // Fetch Users
    builder
      .addCase(fetchUsers.pending, (state) => {
        state.isLoading = true;
        state.error = undefined;
      })
      .addCase(fetchUsers.fulfilled, (state, action) => {
        state.isLoading = false;
        state.users = action.payload;
      })
      .addCase(fetchUsers.rejected, (state, action) => {
        state.isLoading = false;
        state.error = action.payload as string;
      });

    // Role operations moved to separate roles slice

    // Note: fetchCurrentUser cases removed - current user data now comes from auth state

    // Update User Profile
    builder
      .addCase(updateUserProfile.pending, (state) => {
        state.isLoading = true;
        state.error = undefined;
      })
      .addCase(updateUserProfile.fulfilled, (state, action) => {
        state.isLoading = false;
        if (state.currentUser) {
          state.currentUser.profile = action.payload;
        }
      })
      .addCase(updateUserProfile.rejected, (state, action) => {
        state.isLoading = false;
        state.error = action.payload as string;
      });

    // Role CRUD operations moved to separate roles slice

    // Assign Role to User
    builder
      .addCase(assignRoleToUser.pending, (state) => {
        state.isLoading = true;
        state.error = undefined;
      })
      .addCase(assignRoleToUser.fulfilled, (state, action) => {
        state.isLoading = false;
        const userIndex = state.users.findIndex(
          (user) => user.id === action.payload.userId
        );
        if (userIndex !== -1) {
          state.users[userIndex].roleId = action.payload.roleId;
          // Role data will be populated by the roles slice
          state.users[userIndex].role = undefined;
        }
      })
      .addCase(assignRoleToUser.rejected, (state, action) => {
        state.isLoading = false;
        state.error = action.payload as string;
      });

    // Remove Role from User
    builder
      .addCase(removeRoleFromUser.pending, (state) => {
        state.isLoading = true;
        state.error = undefined;
      })
      .addCase(removeRoleFromUser.fulfilled, (state, action) => {
        state.isLoading = false;
        const userIndex = state.users.findIndex(
          (user) => user.id === action.payload
        );
        if (userIndex !== -1) {
          state.users[userIndex].roleId = undefined;
          state.users[userIndex].role = undefined;
        }
      })
      .addCase(removeRoleFromUser.rejected, (state, action) => {
        state.isLoading = false;
        state.error = action.payload as string;
      });

    // Role permissions management moved to separate roles slice

    // Fetch Available Permissions
    builder
      .addCase(fetchAvailablePermissions.pending, (state) => {
        state.isLoading = true;
        state.error = undefined;
      })
      .addCase(fetchAvailablePermissions.fulfilled, (state, action) => {
        state.isLoading = false;
        // Store available permissions in state if needed
      })
      .addCase(fetchAvailablePermissions.rejected, (state, action) => {
        state.isLoading = false;
        state.error = action.payload as string;
      });
  },
});

export const { setLoading, setError, clearError, updateCurrentUser } =
  rbacSlice.actions;

// Selectors
export const selectRBAC = (state: RootState) => state.rbac;
export const selectUsers = (state: RootState) => state.rbac.users;
export const selectCurrentUser = (state: RootState) => state.rbac.currentUser;
export const selectRBACLoading = (state: RootState) => state.rbac.isLoading;
export const selectRBACError = (state: RootState) => state.rbac.error;

// Derived selectors
export const selectUsersWithRoles = (state: RootState) => {
  const users = state.rbac.users || [];
  const roles = state.roles?.roles || []; // Get roles from roles slice

  return users.map((user: any) => ({
    ...user,
    role: user.roleId
      ? roles.find((role: any) => role.id === user.roleId)
      : undefined,
  }));
};

export const selectUsersByRole = (roleId: string) => (state: RootState) => {
  return state.rbac.users.filter((user) => user.roleId === roleId);
};

// Note: selectRoleById moved to roles slice

export default rbacSlice.reducer;

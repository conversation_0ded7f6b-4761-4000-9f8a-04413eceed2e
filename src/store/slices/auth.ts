import { createSlice, PayloadAction } from "@reduxjs/toolkit";
import { Session } from "next-auth";

export interface User extends Session {
  user: Session["user"] & {
    role: Role;
    profile: Profile;
  };
}

export interface Profile {
  id: string;
  about: string;
  website: string;
  socials: string[];
  location: string;
}

export interface Role {
  id: string;
  name: string;
  description: string;
  status: string;
  permissions: string[];
}

// Define the auth state interface
export interface AuthState {
  session: Session | null;
  isAuthenticated: boolean;
  isLoading: boolean;
  isDashboardView: boolean;
  user: {
    id?: string;
    name?: string | null;
    email?: string | null;
    image?: string | null;
    role: Role;
    profile: Profile;
  } | null;
}

// Initial state
const initialState: AuthState = {
  session: null,
  isAuthenticated: false,
  isLoading: true,
  isDashboardView: false,
  user: {
    id: "",
    name: "",
    email: "",
    image: "",
    role: {
      id: "",
      name: "",
      description: "",
      status: "",
      permissions: [],
    },
    profile: {
      id: "",
      about: "",
      website: "",
      socials: [],
      location: "",
    },
  },
};

// Create the auth slice
const authSlice = createSlice({
  name: "auth",
  initialState,
  reducers: {
    // Set session data from next-auth
    setSession: (state, action: PayloadAction<User>) => {
      state.session = action.payload;
      state.isAuthenticated = !!action.payload;
      state.isLoading = false;

      if (action.payload?.user) {
        state.user = {
          id: action.payload.user.id,
          name: action.payload.user.name,
          email: action.payload.user.email,
          image: action.payload.user.image,
          role: action.payload.user.role,
          profile: action.payload.user.profile,
        };
      } else {
        state.user = null;
      }
    },

    // Set loading state
    setLoading: (state, action: PayloadAction<boolean>) => {
      state.isLoading = action.payload;
    },

    // Clear session (logout)
    clearSession: (state) => {
      state.session = null;
      state.isAuthenticated = false;
      state.isLoading = false;
      state.user = null;
    },

    // Update user profile data
    updateUser: (state, action: PayloadAction<Partial<AuthState["user"]>>) => {
      if (state.user) {
        state.user = { ...state.user, ...action.payload };
      }
    },

    // Set dashboard view state
    setDashboardView: (state, action: PayloadAction<boolean>) => {
      state.isDashboardView = action.payload;
    },

    // Toggle dashboard view state
    toggleDashboardView: (state) => {
      state.isDashboardView = !state.isDashboardView;
    },
  },
});

// Export actions
export const {
  setSession,
  setLoading,
  clearSession,
  updateUser,
  setDashboardView,
  toggleDashboardView,
} = authSlice.actions;

// Export selectors
export const selectAuth = (state: { auth: AuthState }) => state.auth;
export const selectRole = (state: { auth: AuthState }) => state.auth.user?.role;
export const selectProfile = (state: { auth: AuthState }) =>
  state.auth.user?.profile;
export const selectPersonlizedRoute = (state: { auth: AuthState }) =>
  state.auth.user?.name?.trim().replace(" ", "-").toLowerCase();
export const selectSession = (state: { auth: AuthState }) => state.auth.session;
export const selectUser = (state: { auth: AuthState }) => state.auth.user;
export const selectIsAuthenticated = (state: { auth: AuthState }) =>
  state.auth.isAuthenticated;
export const selectIsLoading = (state: { auth: AuthState }) =>
  state.auth.isLoading;
export const selectIsDashboardView = (state: { auth: AuthState }) =>
  state.auth.isDashboardView;

// Export reducer
export default authSlice;

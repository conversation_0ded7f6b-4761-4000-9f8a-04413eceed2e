import type { TestimonyCardProps } from "./types";

export const TestimonyCard = ({
  company,
  message,
  from,
}: TestimonyCardProps) => {
  return (
    <span className="w-full border border-white p-6 flex flex-col gap-12">
      <img
        className="w-[100px]"
        src={`/svg/companies/${company}.svg`}
        width={"auto"}
        height={"200"}
        alt={""}
      />
      <span className="h-full flex flex-col gap-8">
        <p className="">{message}</p>
        <span className="h-full flex flex-col justify-end gap-1">
          <p className="font-bold">{from?.name}</p>
          <p>{from?.designation}</p>
        </span>
      </span>
    </span>
  );
};

import Link from "next/link";
import {
  FaLinkedin as LinkedIn,
  <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> as <PERSON><PERSON><PERSON>,
  FaInstagram as Insta<PERSON>,
  FaGithub as Github,
} from "react-icons/fa";
import { FaXTwitter as Twitter, FaThreads as Threads } from "react-icons/fa6";
import type { TeamProps } from "./types";

export const Team = ({
  img,
  name,
  title,
  style = "",
  socials = [],
}: TeamProps) => {
  const props = { size: 20 };

  const Social = {
    linkedin: <LinkedIn {...props} />,
    twitter: <Twitter {...props} />,
    dribbble: <Dribbble {...props} />,
    threads: <Threads {...props} />,
    instagram: <Instagram {...props} />,
    github: <Github {...props} />,
  };

  return (
    <span className={`w-full xl:w-max flex flex-col gap-8 ${style}`}>
      {/* Image */}
      <img
        className="w-full xl:max-w-2xl"
        src={img}
        width={"1080"}
        height={"1080"}
        alt={`team member ${name} card`}
      />
      <span className="w-full flex flex-col">
        {/* Headline */}
        <h6 className="font-medium">{name}</h6>
        {/* Caption */}
        <p className="capitalize">{title}</p>
      </span>
      {/* Links */}
      <span className="w-full flex flex-row justify-start items-center gap-4">
        {socials?.map((link, index) => {
          const { url, name: icon } = link ?? {};
          return (
            <Link key={index} href={url} target="_blank">
              {Social[icon]}
            </Link>
          );
        })}
      </span>
    </span>
  );
};

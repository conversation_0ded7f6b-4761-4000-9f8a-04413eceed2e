import type { ServiceProps } from "./types";

export const Service = ({ img, title, description }: ServiceProps) => {
  return (
    <span className={`w-full flex flex-col gap-4 items-start justify-center`}>
      <img
        className="w-max h-max"
        src={img?.url}
        width={`${img?.width}px`}
        height={`${img?.height}px`}
        alt={`underscore's ${title} card`}
      />
      <h5 className="text-lime-250">{title}</h5>
      <p
        className={`regular-para2 text-white md:max-w-md lg:max-w-lg xl:max-w-2xl`}
      >
        {description}
      </p>
    </span>
  );
};

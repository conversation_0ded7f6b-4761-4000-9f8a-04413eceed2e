"use client";

import { useEffect, useState } from "react";
import Link from "next/link";

import { useRouter } from "next/navigation";
import { removeSpaces } from "@/lib/common/utils";
import type { ProductProps } from "./types";

export const Product = ({
  id,
  image,
  title,
  caption,
  categories = [],
  price,
}: ProductProps) => {
  const navigate = useRouter();

  return (
    <span
      className={`w-full h-[25em] flex flex-col gap-8 cursor-pointer bg-lime-500`}
    >
      {/* Image */}
      <span
        //
        className={`w-full h-full relative bg-lime-450 overflow-hidden`}
      >
        <img
          //
          className="w-full h-full origin-right object-cover"
          //
          src={(window.location.origin ?? "") + (image?.url ?? "")}
          width={"7680"}
          height={"4320"}
          alt={`underscore's ${title} card`}
        />
      </span>
      <span className="w-full h-full flex flex-col gap-4">
        <span className="w-full h-full flex flex-col items-start gap-3">
          {/* title */}
          <p className={"text-white text-xl"}>{title}</p>
          {/* Categories */}
          <span className="w-max flex flex-row items-start gap-1">
            {categories?.map((category, index) => {
              return (
                <p
                  key={index}
                  className="uppercase text-sm text-lime-300 font-semibold"
                >
                  {category?.name}
                </p>
              );
            })}
          </span>
        </span>
        {/* Price */}
        <span className="w-full flex flex-row justify-between items-center font-bold text-white text-lg">
          <p>Price</p>
          <p>${price}</p>
        </span>
        {/* Call to action */}
        <button
          className="w-full bg-gradient-to-r from-lime-450 via-lime-400 to-lime-450 border border-lime-350 font-medium"
          onClick={() => {
            navigate.push(`/market/${removeSpaces(title ?? "")}/${id}`);
          }}
        >
          check it out
        </button>
      </span>
    </span>
  );
};

import Link from "next/link";

import { EmailSubscriber } from "@/modules";

import { IoRemoveOutline as Outline } from "react-icons/io5";
import { HiOutlineArrowSmallRight as RightArrow } from "react-icons/hi2";
import type { FooterProps, ContactItem } from "./types";

export const Footer = ({}: FooterProps) => {
  const year = new Date().getFullYear();

  return (
    <footer>
      <span className="globe"></span>
      <section>
        {/* Directive */}
        <span className="w-full flex flex-row justfy-start items-center gap-4 mb-8">
          <RightArrow size={30} color={"#ffffff"} />
          <h6 className="capitalize">lets connect</h6>
        </span>
        {/* Convincing */}
        <span className="w-full flex flex-col gap-8 md:gap-12 xl:gap-0 xl:flex-row xl:justify-between xl:gap-12 2xl:gap-0 xl:items-start">
          {/* Question */}
          <span className="w-full">
            <h3 className="inline-block">
              Have a project in mind? <br /> Lets make great stuff <br />{" "}
              together
            </h3>
          </span>
          {/* Call to action */}
          <span className="w-full flex flex-col items-start gap-6 md:gap-8 xl:gap-4">
            <EmailSubscriber
              data={{
                interest: ["newsletter"],
              }}
              props={{
                placeholder: "Join to our weekly newsletter",
                button: "subscribe",
                theme: "dark",
              }}
            />
            <h5>
              Our team will review your submission <br /> and get back to you as
              soon as possible
            </h5>
            <Link
              href="mailto:<EMAIL>?subject=Inquiry%20From%20Client"
              className="outline white px-4 py-2 h-full flex flex-row justify-center items-center gap-3"
            >
              <p>Get a quote</p> <RightArrow className="mt-[2px]" size={18} />
            </Link>
          </span>
        </span>
      </section>

      {/* Contacts */}
      <section className="py-4">
        <Contacts />
      </section>

      <section className="w-full">
        {/* foot */}
        <span className="w-full flex flex-col gap-4 lg:gap-12 items-center xl:items-end xl:gap-0 xl:flex-row xl:justify-between">
          <Link href="" className="text-balance">
            {year} &#169; Underscor. All rights reserved
          </Link>
          <img
            className="w-full max-w-52 lg:max-w-96 py-8 lg:py-0"
            src="/svg/underscor.svg"
            width={"280"}
            alt=""
          />
          <span className="gap-2 flex flex-row justify-center">
            <Link href="">Privacy policy</Link>
            <span className="rotate-90">
              <Outline size={25} />
            </span>
            <Link href="">Terms of service</Link>
          </span>
        </span>
      </section>
    </footer>
  );
};

const Contacts = () => {
  return (
    <span className="w-full flex flex-col xl:flex-row">
      {[
        {
          title: "Email",
          email: "<EMAIL>",
          url: "mailto:<EMAIL>?subject=New Client Inquiry",
        },
        {
          title: "linkedin",
          email: "underscor",
          url: "https://www.linkedin.com/company/underscor-io/?viewAsMember=true",
        },
        {
          title: "upwork",
          email: "@underscor",
          url: "https://www.upwork.com/agencies/1655373505334222848/",
        },
        {
          title: "instagram",
          email: "@underscor.io",
          url: "https://www.instagram.com/underscor.io",
        },
      ].map((contact, index) => {
        const { title, email, url } = contact ?? {};
        return (
          <Link
            key={index}
            href={url ?? ""}
            target="blank"
            className={`w-full border-l xl:border-l-transparent xl:border-r border-b border-zinc-700 flex flex-col p-6 xl:py-8 xl:px-16 gap-2`}
          >
            <h6 className="capitalize">{title}</h6>
            <p>{email}</p>
          </Link>
        );
      })}
    </span>
  );
};

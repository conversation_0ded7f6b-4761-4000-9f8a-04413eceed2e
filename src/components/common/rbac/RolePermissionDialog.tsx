"use client";

/**
 * Role Permission Management Dialog
 *
 * A dialog component for managing role permissions with CRUD operations
 * (create, update, view, delete) for different entities.
 *
 * Data structure: { [entity]: ['create', 'update', 'delete', 'view'], ... }
 */

import React, { useState, useEffect, useMemo, useCallback } from "react";
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogDescription,
  DialogFooter,
} from "@/components/common/ui/dialog";
import { Button } from "@/components/common/ui/button";
import { Input } from "@/components/common/ui/input";
import { Label } from "@/components/common/ui/label";
import { Badge } from "@/components/common/ui/badge";
import { Checkbox } from "@/components/common/ui/checkbox";
import { Textarea } from "@/components/common/ui/textarea";

import {
  Tabs,
  Tabs<PERSON>ontent,
  <PERSON><PERSON><PERSON><PERSON>,
  TabsTrigger,
} from "@/components/common/ui/tabs";
import { useRoleManagement } from "@/hooks/useRBAC";
import { useAuth } from "@/hooks/useAuth";
import { toast } from "sonner";

import { Shield, Save, Trash2, FileText } from "lucide-react";

interface RolePermissionDialogProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  roleId?: string; // Optional for create mode, required for edit/view
  mode?: "create" | "edit" | "view"; // Support all three modes
  onSuccess?: () => void;
}

// Default permissions for all entities
const DEFAULT_PERMISSIONS = ["create", "update", "delete", "read"] as const;

// Available entities
const AVAILABLE_ENTITIES = [
  "user",
  "role",
  "document",
  "proposal",
  "contract",
  "room",
  "member",
] as const;

// Permissions data structure: { [entity]: string[] }
type EntityPermissions = Record<string, string[]>;

export function RolePermissionDialog({
  open,
  onOpenChange,
  roleId,
  mode = "create",
  onSuccess,
}: RolePermissionDialogProps) {
  const { roles, createRole, updateRole, deleteRole } = useRoleManagement();

  // Get current user's role and permissions from useAuth
  const { role: currentUserRole } = useAuth();

  // Check if current user can manage roles based on their permissions
  const canManageRoles = useMemo(() => {
    if (!currentUserRole?.permissions) return false;

    // Handle JSON-based permissions structure
    if (
      typeof currentUserRole.permissions === "object" &&
      !Array.isArray(currentUserRole.permissions)
    ) {
      const permissions = currentUserRole.permissions as EntityPermissions;
      const rolePermissions = permissions.role || [];
      return (
        rolePermissions.includes("create") ||
        rolePermissions.includes("update") ||
        rolePermissions.includes("delete") ||
        rolePermissions.includes("read")
      );
    }

    return false;
  }, [currentUserRole?.permissions]);

  const [roleName, setRoleName] = useState("");
  const [roleDescription, setRoleDescription] = useState("");
  const [permissions, setPermissions] = useState<EntityPermissions>({});
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [activeTab, setActiveTab] = useState("details");

  // Get current role data for edit/view modes
  const currentRole = Array.isArray(roles)
    ? roles.find((role) => role.id === roleId)
    : undefined;

  // Initialize form when dialog opens or role changes
  useEffect(() => {
    if (open) {
      if (mode === "create") {
        // Initialize empty form for create mode
        setRoleName("");
        setRoleDescription("");

        // Initialize all entities with empty permissions
        const emptyPermissions: EntityPermissions = {};
        AVAILABLE_ENTITIES.forEach((entity) => {
          emptyPermissions[entity] = [];
        });
        setPermissions(emptyPermissions);
      } else if ((mode === "edit" || mode === "view") && currentRole) {
        // Initialize form with existing role data
        setRoleName(currentRole.name || "");
        setRoleDescription(currentRole.description || "");

        // Initialize permissions from current role
        const rolePermissions: EntityPermissions = {};

        // Initialize all entities with empty arrays first
        AVAILABLE_ENTITIES.forEach((entity) => {
          rolePermissions[entity] = [];
        });

        // Handle JSON-based permissions structure: { [entity]: ['action1', 'action2'] }
        if (
          currentRole.permissions &&
          typeof currentRole.permissions === "object"
        ) {
          const currentPermissions =
            currentRole.permissions as EntityPermissions;

          // Then populate with actual permissions
          Object.entries(currentPermissions).forEach(([entity, actions]) => {
            if (Array.isArray(actions)) {
              rolePermissions[entity] = [...actions];
            }
          });
        }

        setPermissions(rolePermissions);
      }
    }
  }, [
    open,
    mode,
    currentRole?.id,
    currentRole?.name,
    currentRole?.description,
  ]);

  // Check if current user can grant specific permissions
  const canGrantPermission = useCallback(
    (entity: string, action: string) => {
      if (!currentUserRole?.permissions) return false;

      // Handle JSON-based permissions structure
      if (
        typeof currentUserRole.permissions === "object" &&
        !Array.isArray(currentUserRole.permissions)
      ) {
        const userPermissions =
          currentUserRole.permissions as EntityPermissions;

        // User can only grant permissions they have themselves
        const entityPermissions = userPermissions[entity] || [];
        return entityPermissions.includes(action);
      }

      return false;
    },
    [currentUserRole?.permissions]
  );

  const handlePermissionToggle = (entity: string, action: string) => {
    if (mode === "view") return;

    // Check if user can grant this permission
    if (!canGrantPermission(entity, action)) {
      return; // Silently ignore if user doesn't have permission
    }

    setPermissions((prev) => {
      const entityPermissions = prev[entity] || [];
      const hasPermission = entityPermissions.includes(action);

      if (hasPermission) {
        // Remove permission
        return {
          ...prev,
          [entity]: entityPermissions.filter((p) => p !== action),
        };
      } else {
        // Add permission
        return {
          ...prev,
          [entity]: [...entityPermissions, action],
        };
      }
    });
  };

  const handleSelectAllForEntity = (entity: string, selected: boolean) => {
    if (mode === "view") return;

    setPermissions((prev) => {
      if (selected) {
        // Add all permissions that user can grant
        const grantablePermissions = DEFAULT_PERMISSIONS.filter((action) =>
          canGrantPermission(entity, action)
        );
        return {
          ...prev,
          [entity]: grantablePermissions,
        };
      } else {
        // Remove all permissions
        return {
          ...prev,
          [entity]: [],
        };
      }
    });
  };

  const handleSubmit = async () => {
    if (!roleName.trim()) {
      toast.error("Role name is required");
      return;
    }

    // Check if user has permission to manage roles
    if (!canManageRoles) {
      toast.error("You don't have permission to manage roles");
      return;
    }

    // Validate that user can grant all selected permissions
    const invalidPermissions: string[] = [];
    Object.entries(permissions).forEach(([entity, actions]) => {
      actions.forEach((action) => {
        if (!canGrantPermission(entity, action)) {
          invalidPermissions.push(`${entity}:${action}`);
        }
      });
    });

    if (invalidPermissions.length > 0) {
      toast.error(
        `You don't have permission to grant: ${invalidPermissions.join(", ")}`
      );
      return;
    }

    setIsSubmitting(true);

    try {
      // Filter out empty permission arrays
      const newPermissions: EntityPermissions = {};
      Object.entries(permissions).forEach(([entity, actions]) => {
        if (actions.length > 0) {
          newPermissions[entity] = actions;
        }
      });

      if (mode === "create") {
        // Create new role - createRole expects (name, permissions) parameters
        const result = await createRole(roleName.trim(), newPermissions);

        if (!result.success) {
          // Store action already shows toast, just return
          return;
        }

        // Update role description if provided
        if (roleDescription.trim() && result.data?.id) {
          await updateRole(result.data.id, {
            description: roleDescription.trim(),
            permissions: newPermissions,
          });
        }
      } else if (mode === "edit" && currentRole) {
        // Update existing role
        const result = await updateRole(currentRole.id, {
          name: roleName.trim(),
          description: roleDescription.trim(),
          permissions: newPermissions,
        });

        if (!result.success) {
          // Store action already shows toast, just return
          return;
        }
      }

      // Success - refresh data and close dialog
      onSuccess?.();
      onOpenChange(false);
    } catch (error) {
      toast.error(
        error instanceof Error ? error.message : "Failed to save role"
      );
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleDelete = async () => {
    if (!currentRole || mode !== "edit") return;

    if (
      !confirm(
        `Are you sure you want to delete the role "${currentRole.name}"? This action cannot be undone.`
      )
    ) {
      return;
    }

    setIsSubmitting(true);
    try {
      const result = await deleteRole(currentRole.id);
      if (!result.success) {
        // Store action already shows toast, just return
        return;
      }

      onSuccess?.();
      onOpenChange(false);
    } catch (error) {
      toast.error(
        error instanceof Error ? error.message : "Failed to delete role"
      );
    } finally {
      setIsSubmitting(false);
    }
  };

  const getPermissionCount = () => {
    return Object.values(permissions).reduce(
      (total, entityPerms) => total + entityPerms.length,
      0
    );
  };

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="sm:max-w-3xl max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle className="flex flex-row items-center gap-4">
            <Shield className="h-5 w-5" />
            {mode === "create"
              ? "Create Role"
              : mode === "edit"
              ? "Edit Role"
              : "View Role"}
          </DialogTitle>
          <DialogDescription>
            {mode === "create"
              ? "Create a new role with specific permissions"
              : mode === "edit"
              ? "Modify role details and permissions based on your current permissions"
              : "View role details and permissions"}
          </DialogDescription>
        </DialogHeader>

        <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full">
          <TabsList className="grid w-full grid-cols-2">
            <TabsTrigger value="details" className="flex items-center gap-2">
              <FileText className="h-4 w-4" />
              Role Details
            </TabsTrigger>
            <TabsTrigger
              value="permissions"
              className="flex items-center gap-2"
            >
              <Shield className="h-4 w-4" />
              Permissions ({getPermissionCount()})
            </TabsTrigger>
          </TabsList>

          <TabsContent value="details" className="space-y-4">
            <div className="space-y-4">
              <div className="space-y-2">
                <Label htmlFor="role-name">Role Name *</Label>
                <Input
                  id="role-name"
                  value={roleName}
                  onChange={(e) => setRoleName(e.target.value)}
                  placeholder="Enter role name"
                  disabled={mode === "view"}
                />
              </div>

              <div className="space-y-2">
                <Label htmlFor="role-description">Description</Label>
                <Textarea
                  id="role-description"
                  value={roleDescription}
                  onChange={(e) => setRoleDescription(e.target.value)}
                  placeholder="Enter role description"
                  rows={3}
                  disabled={mode === "view"}
                />
              </div>

              {(mode === "edit" || mode === "view") && currentRole && (
                <div className="space-y-2">
                  <Label>Role Information</Label>
                  <div className="grid grid-cols-2 gap-4 p-3 bg-gray-50 rounded-lg">
                    <div>
                      <span className="text-sm font-medium">Created:</span>
                      <p className="text-sm text-gray-600">
                        {new Date(currentRole.createdAt).toLocaleDateString()}
                      </p>
                    </div>
                    <div>
                      <span className="text-sm font-medium">Last Updated:</span>
                      <p className="text-sm text-gray-600">
                        {new Date(
                          currentRole.updatedAt || currentRole.createdAt
                        ).toLocaleDateString()}
                      </p>
                    </div>
                  </div>
                </div>
              )}
            </div>
          </TabsContent>

          <TabsContent value="permissions" className="space-y-4">
            <div className="space-y-4">
              {AVAILABLE_ENTITIES.map((entity) => {
                const entityPermissions = permissions[entity] || [];
                const allSelected = DEFAULT_PERMISSIONS.every((action) =>
                  entityPermissions.includes(action)
                );
                const someSelected = DEFAULT_PERMISSIONS.some((action) =>
                  entityPermissions.includes(action)
                );

                return (
                  <div key={entity} className="border rounded-lg p-4">
                    <div className="flex items-center justify-between mb-3">
                      <div className="flex items-center gap-2">
                        <Checkbox
                          checked={allSelected}
                          ref={(el) => {
                            if (el && el instanceof HTMLInputElement) {
                              el.indeterminate = someSelected && !allSelected;
                            }
                          }}
                          onCheckedChange={(checked) =>
                            handleSelectAllForEntity(entity, checked as boolean)
                          }
                          disabled={
                            mode === "view" ||
                            !DEFAULT_PERMISSIONS.some((action) =>
                              canGrantPermission(entity, action)
                            )
                          }
                          title={
                            !DEFAULT_PERMISSIONS.some((action) =>
                              canGrantPermission(entity, action)
                            )
                              ? "You don't have permission to modify any actions for this entity"
                              : "Select/deselect all permissions for this entity"
                          }
                        />
                        <Label className="font-medium capitalize">
                          {entity.replace("_", " ")}
                          {!DEFAULT_PERMISSIONS.some((action) =>
                            canGrantPermission(entity, action)
                          ) && (
                            <span className="ml-2 text-xs text-orange-500 font-normal">
                              (read-only)
                            </span>
                          )}
                        </Label>
                      </div>
                      <Badge variant="secondary">
                        {entityPermissions.length} /{" "}
                        {DEFAULT_PERMISSIONS.length}
                      </Badge>
                    </div>

                    <div className="grid grid-cols-2 md:grid-cols-4 gap-3 ml-6">
                      {DEFAULT_PERMISSIONS.map((action) => {
                        const isChecked = entityPermissions.includes(action);
                        const canModify = canGrantPermission(entity, action);
                        const isDisabled =
                          mode === "view" || (isChecked && !canModify);

                        return (
                          <div
                            key={action}
                            className="flex items-center space-x-2"
                          >
                            <Checkbox
                              id={`${entity}-${action}`}
                              checked={isChecked}
                              onCheckedChange={() =>
                                handlePermissionToggle(entity, action)
                              }
                              disabled={isDisabled}
                              className={
                                isChecked && !canModify
                                  ? "opacity-60 cursor-not-allowed"
                                  : ""
                              }
                            />
                            <Label
                              htmlFor={`${entity}-${action}`}
                              className={`text-sm capitalize ${
                                isDisabled
                                  ? "text-gray-500 cursor-not-allowed"
                                  : "cursor-pointer"
                              } ${
                                isChecked && !canModify
                                  ? "text-orange-600 font-medium"
                                  : ""
                              }`}
                              title={
                                isChecked && !canModify
                                  ? "You don't have permission to modify this"
                                  : canModify
                                  ? "You can modify this permission"
                                  : "You don't have permission to grant this"
                              }
                            >
                              {action}
                              {isChecked && !canModify && (
                                <span className="ml-1 text-xs text-orange-500">
                                  (read-only)
                                </span>
                              )}
                            </Label>
                          </div>
                        );
                      })}
                    </div>
                  </div>
                );
              })}
            </div>
          </TabsContent>
        </Tabs>

        <DialogFooter className="flex justify-between">
          <div>
            {mode === "edit" && currentRole && (
              <Button
                variant="destructive"
                onClick={handleDelete}
                disabled={isSubmitting}
              >
                <Trash2 className="h-4 w-4 mr-2" />
                Delete Role
              </Button>
            )}
          </div>

          <div className="flex gap-2">
            <Button
              variant="outline"
              onClick={() => onOpenChange(false)}
              disabled={isSubmitting}
            >
              {mode === "view" ? "Close" : "Cancel"}
            </Button>

            {mode !== "view" && (
              <Button
                onClick={handleSubmit}
                disabled={isSubmitting || !roleName.trim()}
              >
                {isSubmitting ? (
                  <>
                    <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
                    {mode === "create" ? "Creating..." : "Saving..."}
                  </>
                ) : (
                  <>
                    <Save className="h-4 w-4 mr-2" />
                    {mode === "create" ? "Create Role" : "Save Changes"}
                  </>
                )}
              </Button>
            )}
          </div>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}

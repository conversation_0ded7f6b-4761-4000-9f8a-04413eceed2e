"use client";

/**
 * User Role Assignment Dialog
 *
 * A dialog component for managing user to role assignments with dynamic user selection
 * and role management functionality.
 */

import React, { useState, useEffect } from "react";
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogDescription,
  DialogFooter,
} from "@/components/common/ui/dialog";
import { Button } from "@/components/common/ui/button";
import { Input } from "@/components/common/ui/input";
import { Label } from "@/components/common/ui/label";
import { Badge } from "@/components/common/ui/badge";

import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/common/ui/select";
import { Separator } from "@/components/common/ui/separator";
import { useRoleManagement, useUserManagement } from "@/hooks/useRBAC";
import { toast } from "sonner";
import { Search, UserPlus, Check } from "lucide-react";

interface UserRoleDialogProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  selectedUserId?: string;
  onSuccess?: () => void;
}

export function UserRoleDialog({
  open,
  onOpenChange,
  selectedUserId,
  onSuccess,
}: UserRoleDialogProps) {
  const { roles = [], isLoading } = useRoleManagement();
  const {
    users = [],
    assignRoleToUser,
    removeRoleFromUser,
  } = useUserManagement();

  const [currentUserId, setCurrentUserId] = useState<string>(
    selectedUserId || ""
  );
  const [searchTerm, setSearchTerm] = useState("");
  const [selectedRoleId, setSelectedRoleId] = useState<string>("");
  const [isSubmitting, setIsSubmitting] = useState(false);

  // Get current user data
  const currentUser = Array.isArray(users)
    ? users.find((user: any) => user.id === currentUserId)
    : undefined;
  const currentUserRoleId = currentUser?.roleId || "";

  // Filter users based on search term
  const filteredUsers = Array.isArray(users)
    ? users.filter(
        (user: any) =>
          user.name?.toLowerCase().includes(searchTerm.toLowerCase()) ||
          user.email.toLowerCase().includes(searchTerm.toLowerCase())
      )
    : [];

  // Reset form when dialog opens/closes or user changes
  useEffect(() => {
    if (open) {
      setCurrentUserId(selectedUserId || "");
      setSelectedRoleId("");
      setSearchTerm("");
    }
  }, [open, selectedUserId]);

  // Update selected role when user changes (only when currentUserId changes)
  useEffect(() => {
    if (currentUserId && currentUser) {
      const userRoleId = currentUser.roleId || "";
      setSelectedRoleId(userRoleId);
    } else if (!currentUserId) {
      setSelectedRoleId("");
    }
  }, [currentUserId, currentUser?.roleId]); // Only depend on currentUserId and the actual roleId

  // Removed unused handleRoleSelect function

  const handleSubmit = async () => {
    if (!currentUserId) {
      toast.error("Please select a user");
      return;
    }

    setIsSubmitting(true);

    try {
      // Handle role assignment/removal
      if (selectedRoleId && selectedRoleId !== currentUserRoleId) {
        // Assign new role
        const result = await assignRoleToUser(currentUserId, selectedRoleId);
        if (result?.meta?.requestStatus !== "fulfilled") {
          // Store action already shows toast, just return
          return;
        }
      } else if (!selectedRoleId && currentUserRoleId) {
        // Remove current role
        const result = await removeRoleFromUser(currentUserId);
        if (result?.meta?.requestStatus !== "fulfilled") {
          // Store action already shows toast, just return
          return;
        }
      }

      // Success
      onSuccess?.();
      onOpenChange(false);
    } catch (error) {
      toast.error(
        error instanceof Error ? error.message : "Failed to update user roles"
      );
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="max-w-2xl max-h-[80vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <UserPlus className="h-5 w-5" />
            Manage User Roles
          </DialogTitle>
          <DialogDescription>
            Assign or revoke roles for users. Changes will be applied
            immediately.
          </DialogDescription>
        </DialogHeader>

        <div className="space-y-6">
          {/* User Selection */}
          <div className="space-y-2">
            <Label htmlFor="user-select">Select User</Label>
            {!selectedUserId ? (
              <div className="space-y-2">
                <div className="relative">
                  <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
                  <Input
                    placeholder="Search users by name or email..."
                    value={searchTerm}
                    onChange={(e) => setSearchTerm(e.target.value)}
                    className="pl-10"
                  />
                </div>

                <Select value={currentUserId} onValueChange={setCurrentUserId}>
                  <SelectTrigger>
                    <SelectValue placeholder="Choose a user" />
                  </SelectTrigger>
                  <SelectContent>
                    {filteredUsers.map((user) => (
                      <SelectItem key={user.id} value={user.id}>
                        <div className="flex flex-col">
                          <span>{user.name || "Unnamed User"}</span>
                          <span className="text-sm text-gray-500">
                            {user.email}
                          </span>
                        </div>
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>
            ) : (
              <div className="p-3 bg-gray-50 rounded-lg">
                <div className="font-medium">
                  {currentUser?.name || "Unnamed User"}
                </div>
                <div className="text-sm text-gray-500">
                  {currentUser?.email}
                </div>
              </div>
            )}
          </div>

          {/* Current User Info */}
          {currentUser && (
            <div className="space-y-3">
              <div className="flex items-center justify-between">
                <Label>Current Role</Label>
                <Badge variant="secondary">
                  {currentUser.role ? "1 role" : "No role"}
                </Badge>
              </div>

              {currentUser.role ? (
                <Badge variant="outline">{currentUser.role.name}</Badge>
              ) : (
                <p className="text-sm text-gray-500">No role assigned</p>
              )}
            </div>
          )}

          <Separator />

          {/* Role Assignment */}
          <div className="space-y-3">
            <Label>Select Role</Label>

            {isLoading ? (
              <div className="flex items-center justify-center py-8">
                <div className="animate-spin rounded-full h-6 w-6 border-b-2 border-gray-900"></div>
              </div>
            ) : roles.length > 0 ? (
              <div className="space-y-4">
                <Select
                  value={selectedRoleId}
                  onValueChange={setSelectedRoleId}
                  disabled={isSubmitting}
                >
                  <SelectTrigger>
                    <SelectValue placeholder="Choose a role (or leave empty for no role)" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="">No Role</SelectItem>
                    {Array.isArray(roles) &&
                      roles.map((role) => (
                        <SelectItem key={role.id} value={role.id}>
                          <div className="flex flex-col">
                            <span>{role.name}</span>
                            <span className="text-sm text-gray-500">
                              {role.permissions?.length || 0} permission
                              {(role.permissions?.length || 0) !== 1 ? "s" : ""}
                            </span>
                          </div>
                        </SelectItem>
                      ))}
                  </SelectContent>
                </Select>

                {/* Show selected role details */}
                {selectedRoleId && (
                  <div className="p-3 bg-blue-50 border border-blue-200 rounded-lg">
                    {(() => {
                      const selectedRole = Array.isArray(roles)
                        ? roles.find((r) => r.id === selectedRoleId)
                        : undefined;
                      return selectedRole ? (
                        <div>
                          <h4 className="font-medium text-blue-800">
                            {selectedRole.name}
                          </h4>
                          <div className="flex items-center gap-2 mt-2">
                            <Badge variant="secondary" className="text-xs">
                              {selectedRole.permissions.length} permission
                              {selectedRole.permissions.length !== 1 ? "s" : ""}
                            </Badge>
                            {selectedRole.status === "active" && (
                              <Badge variant="outline" className="text-xs">
                                Active
                              </Badge>
                            )}
                          </div>
                          {selectedRoleId !== currentUserRoleId && (
                            <p className="text-sm text-blue-600 mt-2">
                              {currentUserRoleId
                                ? "Will replace current role"
                                : "Will assign this role"}
                            </p>
                          )}
                        </div>
                      ) : null;
                    })()}
                  </div>
                )}
              </div>
            ) : (
              <p className="text-sm text-gray-500 py-4">No roles available</p>
            )}
          </div>
        </div>

        <DialogFooter>
          <Button
            variant="outline"
            onClick={() => onOpenChange(false)}
            disabled={isSubmitting}
          >
            Cancel
          </Button>
          <Button
            onClick={handleSubmit}
            disabled={!currentUserId || isSubmitting}
          >
            {isSubmitting ? (
              <>
                <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
                Updating...
              </>
            ) : (
              <>
                <Check className="h-4 w-4 mr-2" />
                Update Role
              </>
            )}
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}

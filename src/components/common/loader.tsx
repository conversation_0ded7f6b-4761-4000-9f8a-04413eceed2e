import { motion } from "framer-motion";

import { Jelly } from "ldrs/react";
import "ldrs/react/Jelly.css";
import type { LoaderProps } from "./types";

export const Loader = ({ active }: LoaderProps) => {
  const transition = { ease: "easeInOut", duration: 0.8 };

  return (
    <motion.div
      transition={transition}
      initial={"show"}
      animate={active ? "show" : "hide"}
      variants={{
        show: {
          height: "100vh",
        },
        hide: {
          height: 0,
          scaleY: 0,
        },
      }}
      className={`w-full transition duration-300 fixed flex flex-col gap-12 items-center justify-center bg-black/[.5] backdrop-blur-md z-20 p-12`}
    >
      {active && <Jelly size="44" speed=".8" color="#00E58A" />}
    </motion.div>
  );
};

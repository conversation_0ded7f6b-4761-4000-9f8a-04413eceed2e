"use client";

import React, { useEffect } from "react";
import dynamic from "next/dynamic";

const ReactQuill = dynamic(() => import("react-quill-new"), { ssr: false });
import { Quill } from "react-quill-new";
import "react-quill-new/dist/quill.snow.css";

const modules: any = {
  toolbar: [
    [{ header: [1, 2, 3, 4, 5, 6, false] }],
    ["bold", "italic", "underline"],
    [{ list: "ordered" }, { list: "bullet" }],
    [{ indent: "-1" }, { indent: "+1" }],
    ["link", "blockquote", "image"],
    ["clean"],
  ],
  history: {
    delay: 1000,
    maxStack: 500,
    userOnly: true,
  },
  clipboard: {
    matchVisual: false,
  },
  keyboard: {
    bindings: {
      linebreak: {
        key: 13,
        shiftKey: true,
        handler: function (this: any, range: any, _context: any) {
          this.quill.insertText(range.index, "\n");
          this.quill.setSelection(range.index + 1, 0);
          return false;
        },
      },
      enter: {
        key: 13,
        handler: function (this: any, range: any, _context: any) {
          this.quill.insertText(range.index, "\n");
          this.quill.setSelection(range.index + 1, 0);
          return false;
        },
      },
    },
  },
};

const formats = [
  "header",
  "bold",
  "italic",
  "underline",
  "strike",
  "blockquote",
  "list", // 'list' is the main format, 'bullet' and 'ordered' are values
  "indent",
  "link",
  "image",
];

export default function RichTextEditor({
  id,
  value,
  onChange,
  placeholder,
}: {
  id: string;
  value: string;
  onChange: (value: string) => void;
  placeholder?: string;
}) {
  useEffect(() => {
    let element: any = document.querySelector(".ql-editor");
    if (element) modules.keyboard.bindings.enter.quill = Quill.find(element);
  }, []);

  return (
    <ReactQuill
      id={id}
      placeholder={placeholder}
      value={value}
      onChange={onChange}
      theme="snow"
      style={{
        width: "100%",
        height: "30em",
        minHeight: "35em",
        maxHeight: "40em",
        resize: "vertical",
        // overflowY: "scroll",
      }}
      modules={modules}
      formats={formats}
      preserveWhitespace
    />
  );
}

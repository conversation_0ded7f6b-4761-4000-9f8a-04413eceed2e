"use client";

import { useState } from "react";
import { motion } from "framer-motion";
import type { ListTabProps, RowTabProps } from "./types";

const transition = {
  duration: 0.4,
  ease: "circInOut",
};

export const themes = {
  lime: "bg-linear-to-r from-lime-500 to-lime-500/60 hover:border-r hover:border-zinc-250",
  navy: "bg-linear-to-r from-navy-250 to-navy-250/60 hover:border-r hover:border-lime-250",
  gray: "bg-linear-to-r from-zinc-900 to-zinc-900/60 hover:border-r hover:border-lime-250",
  black: "bg-black hover:border-r hover:border-lime-250",
  outline:
    "border border-lime-350 hover:border-lime-250 duration-300 bg-lime-500",
};

export const ListTab = ({
  title = "Tab",
  caption = "",
  icon,
  style = { width: "w-full", theme: "lime" },
  position = "start",
}: ListTabProps) => {
  const [hover, setHover] = useState(false);

  return (
    <span className={`w-full flex flex-row justify-${position}`}>
      <span
        onMouseEnter={() => setHover(true)}
        onMouseLeave={() => setHover(false)}
        //
        className={`${style?.width} ${
          themes[style?.theme ?? "lime"]
        } py-4 px-6 flex flex-col gap-4 items-start justify-center overflow-hidden cursor-pointer transition easeInOut duration-300`}
      >
        <h6
          className={`${
            style?.theme === "outline" && hover ? "text-lime-250" : "text-white"
          }`}
        >
          {title}
        </h6>
        <motion.span
          animate={hover ? "expand" : "collapse"}
          variants={{
            expand: {
              height: "max-content",
              opacity: 100,
            },
            collapse: {
              height: 0,
              opacity: 0,
            },
          }}
          transition={transition}
          //
          className={`overflow-hidden inline-block gap-4`}
        >
          <p
            className={`${
              caption ? "flex" : "hidden"
            } text-navy-50/[.85] leading-relaxed`}
          >
            {caption}
          </p>
          <img
            className={icon ? "flex pt-12" : "hidden"}
            src={`/svg/prisms/service/${icon}.svg`}
            width={"70px"}
            height={"70px"}
            alt=""
          />
        </motion.span>
      </span>
    </span>
  );
};

export const RowTab = ({
  title = "Tab",
  caption = "",
  style = "lime",
}: RowTabProps) => {
  const [hover, setHover] = useState(false);

  const themes = {
    lime: "bg-lime-350",
    blue: "bg-navy-200",
    navy: "bg-navy-250",
    gray: "bg-zinc-800",
    black: "bg-zinc-900",
  };

  return (
    <span
      onMouseEnter={() => setHover(true)}
      onMouseLeave={() => setHover(false)}
      className={`w-full h-full ${themes[style]} py-4 px-6 flex flex-col lg:flex-row justify-between overflow-hidden`}
    >
      <h6 className="w-full lg:w-1/2 text-white capitalize">{title}</h6>
      <motion.span
        animate={hover ? "expand" : "collapse"}
        variants={{
          expand: {
            height: "12rem",
          },
          collapse: {
            height: 0,
          },
        }}
        transition={transition}
        //
        className={`w-full lg:w-1/2 h-full flex flex-col justify-end items-end overflow-hidden inline-block gap-4`}
      >
        <motion.p
          animate={hover ? { opacity: 100 } : { opacity: 0 }}
          transition={{ duration: 1, ease: "easeInOut" }}
          className={`${caption ? "flex" : "hidden"} text-slate-300`}
        >
          {caption}
        </motion.p>
      </motion.span>
    </span>
  );
};

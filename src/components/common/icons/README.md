# Icon Usage Guide - React Icons

This project uses `react-icons` for consistent iconography. This guide provides common icon imports and usage patterns.

## Installation

```bash
pnpm add react-icons
```

## Common Icon Libraries

### Font Awesome (FA)
```typescript
import { 
  FaGoogle, 
  FaLinkedin, 
  FaFacebook, 
  FaTwitter, 
  FaGithub,
  FaUser,
  FaEnvelope,
  FaLock,
  FaEye,
  FaEyeSlash
} from "react-icons/fa";
```

### Heroicons (HI)
```typescript
import { 
  HiOutlineUser,
  HiOutlineMail,
  HiOutlineLockClosed,
  HiOutlineEye,
  HiOutlineEyeOff
} from "react-icons/hi";
```

### Material Design (MD)
```typescript
import { 
  MdEmail,
  MdLock,
  MdVisibility,
  MdVisibilityOff,
  Md<PERSON>erson
} from "react-icons/md";
```

### Bootstrap Icons (BS)
```typescript
import { 
  <PERSON>sGoogle,
  BsLinkedin,
  BsFacebook,
  Bs<PERSON><PERSON><PERSON>,
  BsGith<PERSON>
} from "react-icons/bs";
```

## Usage Patterns

### Basic Icon Usage
```typescript
import { FaGoogle } from "react-icons/fa";

function MyComponent() {
  return (
    <button>
      <FaGoogle className="mr-2 h-4 w-4" />
      Sign in with Google
    </button>
  );
}
```

### Icon with Conditional Rendering
```typescript
import { FaEye, FaEyeSlash } from "react-icons/fa";

function PasswordInput() {
  const [showPassword, setShowPassword] = useState(false);
  
  return (
    <div className="relative">
      <input type={showPassword ? "text" : "password"} />
      <button onClick={() => setShowPassword(!showPassword)}>
        {showPassword ? (
          <FaEyeSlash className="h-4 w-4" />
        ) : (
          <FaEye className="h-4 w-4" />
        )}
      </button>
    </div>
  );
}
```

### Icon Sizing with Tailwind
```typescript
// Small icons
<FaGoogle className="h-3 w-3" />

// Medium icons (default)
<FaGoogle className="h-4 w-4" />

// Large icons
<FaGoogle className="h-6 w-6" />

// Extra large icons
<FaGoogle className="h-8 w-8" />
```

## Common Icons by Category

### Authentication & Social
```typescript
import { 
  FaGoogle,        // Google
  FaLinkedin,      // LinkedIn
  FaFacebook,      // Facebook
  FaTwitter,       // Twitter
  FaGithub,        // GitHub
  FaMicrosoft,     // Microsoft
  FaApple          // Apple
} from "react-icons/fa";
```

### Form & Input
```typescript
import { 
  FaUser,          // User/Profile
  FaEnvelope,      // Email
  FaLock,          // Password/Security
  FaEye,           // Show password
  FaEyeSlash,      // Hide password
  FaSearch,        // Search
  FaFilter         // Filter
} from "react-icons/fa";
```

### Navigation & Actions
```typescript
import { 
  FaHome,          // Home
  FaBars,          // Menu
  FaTimes,         // Close
  FaChevronLeft,   // Back
  FaChevronRight,  // Forward
  FaPlus,          // Add
  FaEdit,          // Edit
  FaTrash,         // Delete
  FaSave           // Save
} from "react-icons/fa";
```

### Status & Feedback
```typescript
import { 
  FaCheck,         // Success
  FaTimes,         // Error
  FaExclamation,   // Warning
  FaInfo,          // Information
  FaSpinner,       // Loading
  FaBell           // Notifications
} from "react-icons/fa";
```

## Best Practices

### 1. Consistent Sizing
Use consistent sizing classes across your application:
```typescript
// Standard sizes
const iconSizes = {
  xs: "h-3 w-3",
  sm: "h-4 w-4", 
  md: "h-5 w-5",
  lg: "h-6 w-6",
  xl: "h-8 w-8"
};
```

### 2. Semantic Icon Selection
Choose icons that clearly represent their function:
```typescript
// Good
<FaGoogle className="mr-2 h-4 w-4" />
<FaEye className="h-4 w-4" />

// Avoid generic icons for specific actions
<FaCircle className="mr-2 h-4 w-4" /> // Too generic
```

### 3. Accessibility
Always provide proper accessibility attributes:
```typescript
<button aria-label="Toggle password visibility">
  <FaEye className="h-4 w-4" />
</button>
```

### 4. Color and Theming
Use Tailwind classes for consistent theming:
```typescript
// Use semantic colors
<FaGoogle className="text-blue-500" />
<FaLinkedin className="text-blue-700" />

// Use theme-aware colors
<FaUser className="text-muted-foreground" />
<FaCheck className="text-green-500" />
<FaTimes className="text-destructive" />
```

## Migration from Custom SVGs

When replacing custom SVG icons:

### Before (Custom SVG):
```typescript
<svg className="mr-2 h-4 w-4" viewBox="0 0 24 24">
  <path fill="currentColor" d="..." />
</svg>
```

### After (React Icons):
```typescript
import { FaGoogle } from "react-icons/fa";

<FaGoogle className="mr-2 h-4 w-4" />
```

## Performance Considerations

React Icons are tree-shakeable, so only imported icons are included in the bundle:

```typescript
// Good - only imports specific icons
import { FaGoogle, FaLinkedin } from "react-icons/fa";

// Avoid - imports entire library
import * as FaIcons from "react-icons/fa";
```

## Icon Libraries Reference

- **FA**: Font Awesome (most comprehensive)
- **HI**: Heroicons (clean, modern)
- **MD**: Material Design (Google's design system)
- **BS**: Bootstrap Icons (simple, consistent)
- **AI**: Ant Design Icons (enterprise-focused)
- **BI**: BoxIcons (diverse collection)
- **GI**: Game Icons (gaming/entertainment)
- **IO**: Ionicons (mobile-first)

Choose the library that best fits your design system and use it consistently throughout the project.

"use client";

import { <PERSON><PERSON> } from "@/components/common/ui/button";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@/components/common/ui/dialog";
import { AlertTriangle } from "lucide-react";
import type { Contract as ApiContract } from "@/lib/api/validators/schemas/contract";

interface ContractDeleteDialogProps {
  contract: ApiContract;
  isOpen: boolean;
  onOpenChange: (open: boolean) => void;
  onConfirm: () => void;
  isDeleting: boolean;
}

export function ContractDeleteDialog({
  contract,
  isOpen,
  onOpenChange,
  onConfirm,
  isDeleting,
}: ContractDeleteDialogProps) {
  return (
    <Dialog open={isOpen} onOpenChange={onOpenChange}>
      <DialogContent className="sm:max-w-md">
        <DialogHeader>
          <div className="flex items-center space-x-2">
            <div className="flex h-10 w-10 items-center justify-center rounded-full bg-red-100 dark:bg-red-900">
              <AlertTriangle className="h-5 w-5 text-red-600 dark:text-red-400" />
            </div>
            <div>
              <DialogTitle>Delete Contract</DialogTitle>
              <DialogDescription>
                Are you sure you want to delete this contract?
              </DialogDescription>
            </div>
          </div>
        </DialogHeader>

        <div className="space-y-4">
          <div className="rounded-lg border border-gray-200 dark:border-gray-700 p-4">
            <div className="space-y-2">
              <div className="text-sm font-medium text-gray-900 dark:text-white">
                {contract.title}
              </div>
              <div className="text-sm text-gray-500 dark:text-gray-400">
                Client: {contract.client_name}
              </div>
              <div className="text-sm text-gray-500 dark:text-gray-400">
                Value: ${contract.contract_value.toLocaleString()}
              </div>
            </div>
          </div>

          <div className="rounded-lg bg-red-50 dark:bg-red-900/20 p-4">
            <p className="text-sm text-red-800 dark:text-red-200">
              <strong>Warning:</strong> This action cannot be undone. The contract
              and all associated data will be permanently deleted.
            </p>
          </div>
        </div>

        <DialogFooter>
          <Button
            variant="outline"
            onClick={() => onOpenChange(false)}
            disabled={isDeleting}
          >
            Cancel
          </Button>
          <Button
            variant="destructive"
            onClick={onConfirm}
            disabled={isDeleting}
          >
            {isDeleting ? "Deleting..." : "Delete Contract"}
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}

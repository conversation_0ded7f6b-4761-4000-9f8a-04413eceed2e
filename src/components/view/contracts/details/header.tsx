"use client";

import { <PERSON><PERSON> } from "@/components/common/ui/button";
import { Badge } from "@/components/common/ui/badge";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@/components/common/ui/dropdown-menu";
import { ArrowLeft, Edit, Trash2, MoreHorizontal, FileText } from "lucide-react";
import { useRouter } from "next/navigation";
import type { Contract as ApiContract } from "@/lib/api/validators/schemas/contract";

interface ContractDetailsHeaderProps {
  contract: ApiContract;
  onEdit: () => void;
  onDelete: () => void;
}

const statusColors = {
  draft: "bg-gray-100 text-gray-800 dark:bg-gray-800 dark:text-gray-300",
  active: "bg-green-100 text-green-800 dark:bg-green-800 dark:text-green-300",
  completed: "bg-blue-100 text-blue-800 dark:bg-blue-800 dark:text-blue-300",
  terminated: "bg-red-100 text-red-800 dark:bg-red-800 dark:text-red-300",
  expired: "bg-orange-100 text-orange-800 dark:bg-orange-800 dark:text-orange-300",
};

export function ContractDetailsHeader({
  contract,
  onEdit,
  onDelete,
}: ContractDetailsHeaderProps) {
  const router = useRouter();

  const handleBack = () => {
    router.back();
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString("en-US", {
      year: "numeric",
      month: "long",
      day: "numeric",
    });
  };

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat("en-US", {
      style: "currency",
      currency: "USD",
    }).format(amount);
  };

  return (
    <div className="space-y-6">
      {/* Back Button */}
      <Button
        variant="ghost"
        onClick={handleBack}
        className="flex items-center space-x-2 text-gray-600 hover:text-gray-900 dark:text-gray-400 dark:hover:text-gray-100"
      >
        <ArrowLeft className="h-4 w-4" />
        <span>Back to Contracts</span>
      </Button>

      {/* Header Content */}
      <div className="flex flex-col space-y-4 sm:flex-row sm:items-start sm:justify-between sm:space-y-0">
        <div className="flex items-start space-x-4">
          <div className="flex h-12 w-12 items-center justify-center rounded-lg bg-blue-100 dark:bg-blue-900">
            <FileText className="h-6 w-6 text-blue-600 dark:text-blue-400" />
          </div>
          <div className="space-y-2">
            <div className="flex items-center space-x-3">
              <h1 className="text-2xl font-bold tracking-tight text-gray-900 dark:text-white">
                {contract.title}
              </h1>
              <Badge
                variant="secondary"
                className={statusColors[contract.status as keyof typeof statusColors]}
              >
                {contract.status.charAt(0).toUpperCase() + contract.status.slice(1)}
              </Badge>
            </div>
            <div className="flex flex-col space-y-1 text-sm text-gray-500 dark:text-gray-400">
              <div>Client: {contract.client_name}</div>
              <div>Value: {formatCurrency(contract.contract_value)}</div>
              <div>
                Duration: {formatDate(contract.start_date)} - {formatDate(contract.end_date)}
              </div>
            </div>
          </div>
        </div>

        {/* Actions */}
        <div className="flex items-center space-x-2">
          <Button variant="outline" onClick={onEdit}>
            <Edit className="h-4 w-4 mr-2" />
            Edit
          </Button>

          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <Button variant="outline" size="icon">
                <MoreHorizontal className="h-4 w-4" />
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent align="end">
              <DropdownMenuItem onClick={onEdit}>
                <Edit className="mr-2 h-4 w-4" />
                Edit Contract
              </DropdownMenuItem>
              <DropdownMenuItem
                onClick={onDelete}
                className="text-red-600 dark:text-red-400"
              >
                <Trash2 className="mr-2 h-4 w-4" />
                Delete Contract
              </DropdownMenuItem>
            </DropdownMenuContent>
          </DropdownMenu>
        </div>
      </div>

      {/* Contract Meta Information */}
      <div className="grid grid-cols-1 gap-4 sm:grid-cols-2 lg:grid-cols-4">
        <div className="rounded-lg border border-gray-200 dark:border-gray-700 p-4">
          <div className="text-sm font-medium text-gray-500 dark:text-gray-400">
            Contract ID
          </div>
          <div className="mt-1 text-sm font-mono text-gray-900 dark:text-white">
            {contract.id}
          </div>
        </div>
        <div className="rounded-lg border border-gray-200 dark:border-gray-700 p-4">
          <div className="text-sm font-medium text-gray-500 dark:text-gray-400">
            Created
          </div>
          <div className="mt-1 text-sm text-gray-900 dark:text-white">
            {formatDate(contract.createdAt)}
          </div>
        </div>
        <div className="rounded-lg border border-gray-200 dark:border-gray-700 p-4">
          <div className="text-sm font-medium text-gray-500 dark:text-gray-400">
            Last Updated
          </div>
          <div className="mt-1 text-sm text-gray-900 dark:text-white">
            {formatDate(contract.updatedAt)}
          </div>
        </div>
        <div className="rounded-lg border border-gray-200 dark:border-gray-700 p-4">
          <div className="text-sm font-medium text-gray-500 dark:text-gray-400">
            Proposal ID
          </div>
          <div className="mt-1 text-sm font-mono text-gray-900 dark:text-white">
            {contract.proposal_id || "N/A"}
          </div>
        </div>
      </div>
    </div>
  );
}

"use client";

import { useState, useEffect } from "react";
import { useContracts } from "@/hooks/useContracts";
import { ContractDetailsHeader } from "./header";
import { ContractDetailsContent } from "./content";
import { ContractEditDialog } from "./edit-dialog";
import { ContractDeleteDialog } from "./delete-dialog";
import type { Contract as ApiContract } from "@/lib/api/validators/schemas/contract";

interface ContractDetailsContainerProps {
  contractId: string;
}

export function ContractDetailsContainer({
  contractId,
}: ContractDetailsContainerProps) {
  const {
    currentContract,
    isLoading,
    error,
    fetchContract,
    updateContract,
    deleteContract,
    clearError,
  } = useContracts();

  const [isEditDialogOpen, setIsEditDialogOpen] = useState(false);
  const [isDeleteDialogOpen, setIsDeleteDialogOpen] = useState(false);
  const [isUpdating, setIsUpdating] = useState(false);
  const [isDeleting, setIsDeleting] = useState(false);

  useEffect(() => {
    if (contractId) {
      fetchContract(contractId);
    }
  }, [contractId, fetchContract]);

  useEffect(() => {
    if (error) {
      console.error("Contract details error:", error);
    }
  }, [error]);

  // Clear error when component unmounts
  useEffect(() => {
    return () => {
      clearError();
    };
  }, [clearError]);

  const handleEdit = () => {
    setIsEditDialogOpen(true);
  };

  const handleDelete = () => {
    setIsDeleteDialogOpen(true);
  };

  const handleUpdateContract = async (data: Partial<ApiContract>) => {
    if (!currentContract) return;

    setIsUpdating(true);
    try {
      await updateContract(currentContract.id, data);
      setIsEditDialogOpen(false);
    } catch (error) {
      console.error("Failed to update contract:", error);
    } finally {
      setIsUpdating(false);
    }
  };

  const handleDeleteContract = async () => {
    if (!currentContract) return;

    setIsDeleting(true);
    try {
      await deleteContract(currentContract.id);
      setIsDeleteDialogOpen(false);
      // Redirect will be handled by the hook or parent component
    } catch (error) {
      console.error("Failed to delete contract:", error);
    } finally {
      setIsDeleting(false);
    }
  };

  if (isLoading) {
    return (
      <div className="grid grid-cols-1 gap-12 p-4">
        <div className="animate-pulse">
          <div className="h-8 bg-gray-200 dark:bg-gray-700 rounded w-1/3 mb-4"></div>
          <div className="h-4 bg-gray-200 dark:bg-gray-700 rounded w-1/2 mb-8"></div>
          <div className="space-y-4">
            <div className="h-32 bg-gray-200 dark:bg-gray-700 rounded"></div>
            <div className="h-32 bg-gray-200 dark:bg-gray-700 rounded"></div>
            <div className="h-32 bg-gray-200 dark:bg-gray-700 rounded"></div>
          </div>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="grid grid-cols-1 gap-12 p-4">
        <div className="text-center py-12">
          <h2 className="text-2xl font-bold text-gray-900 dark:text-white mb-2">
            Contract Not Found
          </h2>
          <p className="text-gray-500 dark:text-gray-400 mb-4">
            The contract you're looking for doesn't exist or you don't have permission to view it.
          </p>
        </div>
      </div>
    );
  }

  if (!currentContract) {
    return (
      <div className="grid grid-cols-1 gap-12 p-4">
        <div className="text-center py-12">
          <h2 className="text-2xl font-bold text-gray-900 dark:text-white mb-2">
            Loading Contract...
          </h2>
        </div>
      </div>
    );
  }

  return (
    <div className="grid grid-cols-1 gap-12 p-4">
      <ContractDetailsHeader
        contract={currentContract}
        onEdit={handleEdit}
        onDelete={handleDelete}
      />

      <ContractDetailsContent contract={currentContract} />

      {/* Edit Dialog */}
      <ContractEditDialog
        contract={currentContract}
        isOpen={isEditDialogOpen}
        onOpenChange={setIsEditDialogOpen}
        onSubmit={handleUpdateContract}
        isUpdating={isUpdating}
      />

      {/* Delete Dialog */}
      <ContractDeleteDialog
        contract={currentContract}
        isOpen={isDeleteDialogOpen}
        onOpenChange={setIsDeleteDialogOpen}
        onConfirm={handleDeleteContract}
        isDeleting={isDeleting}
      />
    </div>
  );
}

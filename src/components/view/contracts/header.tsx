"use client";

import { But<PERSON> } from "@/components/common/ui/button";
import { FileText, Plus } from "lucide-react";

interface ContractHeaderProps {
  onCreateContract: () => void;
  contractsCount: number;
}

export function ContractHeader({
  onCreateContract,
  contractsCount,
}: ContractHeaderProps) {
  return (
    <div className="flex flex-col space-y-4 sm:flex-row sm:items-center sm:justify-between sm:space-y-0">
      <div className="flex flex-col items-start gap-3">
        <h1 className="text-2xl font-bold tracking-tight text-gray-900 dark:text-white">
          Contracts
        </h1>
        <p className="text-sm text-gray-500 dark:text-gray-400">
          Manage your client contracts and agreements, {contractsCount}{" "}
          {contractsCount === 1 ? "contract" : "contracts"}
        </p>
      </div>

      <div className="flex flex-col items-end gap-3">
        <div className="hidden sm:block"></div>
        <Button
          hidden={true}
          onClick={onCreateContract}
          className="bg-blue-600 hover:bg-blue-700 dark:bg-blue-600 dark:hover:bg-blue-700"
        >
          <Plus className="h-4 w-4 mr-2" />
          New Contract
        </Button>
      </div>
    </div>
  );
}

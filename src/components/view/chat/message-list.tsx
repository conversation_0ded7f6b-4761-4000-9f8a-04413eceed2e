"use client";

import React, { useEffect, useRef } from "react";
import {
  Avatar,
  AvatarFallback,
  AvatarImage,
} from "@/components/common/ui/avatar";
import { Badge } from "@/components/common/ui/badge";
import { formatDistanceToNow } from "date-fns";
import { cn } from "@/lib/utils";

interface Message {
  id: string;
  content: string;
  sent_from: string;
  createdAt: Date;
  sender?: {
    id: string;
    name: string;
    email: string;
    avatar: string;
  };
  associations?: any[];
}

interface MessageListProps {
  messages: Message[];
  currentUserId?: string;
  isLoading?: boolean;
  className?: string;
}

interface MessageItemProps {
  message: Message;
  isOwn: boolean;
  showAvatar: boolean;
  showTimestamp: boolean;
}

function MessageItem({
  message,
  isOwn,
  showAvatar,
  showTimestamp,
}: MessageItemProps) {
  const senderName = message.sender?.name || "Unknown User";
  const senderInitials = senderName
    .split(" ")
    .map((n) => n[0])
    .join("")
    .toUpperCase()
    .slice(0, 2);

  return (
    <div
      className={cn(
        "flex gap-3 group",
        isOwn ? "flex-row-reverse" : "flex-row"
      )}
    >
      {/* Avatar */}
      <div
        className={cn(
          "flex-shrink-0",
          showAvatar ? "opacity-100" : "opacity-0"
        )}
      >
        <Avatar className="h-8 w-8">
          <AvatarImage src={message.sender?.avatar} alt={senderName} />
          <AvatarFallback className="text-xs">{senderInitials}</AvatarFallback>
        </Avatar>
      </div>

      {/* Message content */}
      <div
        className={cn(
          "flex flex-col max-w-[70%]",
          isOwn ? "items-end" : "items-start"
        )}
      >
        {/* Sender name and timestamp */}
        {showTimestamp && (
          <div
            className={cn(
              "flex items-center gap-2 mb-1",
              isOwn ? "flex-row-reverse" : "flex-row"
            )}
          >
            <span className="text-sm font-medium text-gray-900">
              {isOwn ? "You" : senderName}
            </span>
            <span className="text-xs text-gray-500">
              {formatDistanceToNow(new Date(message.createdAt), {
                addSuffix: true,
              })}
            </span>
          </div>
        )}

        {/* Message bubble */}
        <div
          className={cn(
            "rounded-lg px-3 py-2 max-w-full break-words",
            isOwn ? "bg-blue-600 text-white" : "bg-gray-100 text-gray-900"
          )}
        >
          {/* Message content */}
          <div
            className="prose prose-sm max-w-none"
            dangerouslySetInnerHTML={{ __html: message.content }}
          />

          {/* Associations (forms, files, etc.) */}
          {message.associations && message.associations.length > 0 && (
            <div className="mt-2 space-y-1">
              {message.associations.map((association, index) => (
                <Badge key={index} variant="secondary" className="text-xs">
                  {association.type}: {association.title || association.id}
                </Badge>
              ))}
            </div>
          )}
        </div>

        {/* Timestamp on hover */}
        {!showTimestamp && (
          <span className="text-xs text-gray-500 opacity-0 group-hover:opacity-100 transition-opacity mt-1">
            {formatDistanceToNow(new Date(message.createdAt), {
              addSuffix: true,
            })}
          </span>
        )}
      </div>
    </div>
  );
}

export function MessageList({
  messages,
  currentUserId,
  isLoading = false,
  className,
}: MessageListProps) {
  const messagesEndRef = useRef<HTMLDivElement>(null);
  const containerRef = useRef<HTMLDivElement>(null);

  // Auto-scroll to bottom when new messages arrive
  useEffect(() => {
    if (messagesEndRef.current) {
      messagesEndRef.current.scrollIntoView({ behavior: "smooth" });
    }
  }, [messages]);

  if (isLoading) {
    return (
      <div className={cn("flex-1 flex items-center justify-center", className)}>
        <div className="text-center">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto mb-2"></div>
          <p className="text-sm text-gray-500">Loading messages...</p>
        </div>
      </div>
    );
  }

  if (messages.length === 0) {
    return (
      <div className={cn("flex-1 flex items-center justify-center", className)}>
        <div className="text-center">
          <p className="text-gray-500 mb-2">No messages yet</p>
          <p className="text-sm text-gray-400">Start the conversation!</p>
        </div>
      </div>
    );
  }

  return (
    <div
      ref={containerRef}
      className={cn("flex-1 overflow-y-auto p-4 space-y-4", className)}
    >
      {messages.map((message, index) => {
        const isOwn = message.sent_from === currentUserId;
        const prevMessage = index > 0 ? messages[index - 1] : null;
        const nextMessage =
          index < messages.length - 1 ? messages[index + 1] : null;

        // Show avatar if it's the first message from this sender in a group
        const showAvatar =
          !prevMessage || prevMessage.sent_from !== message.sent_from;

        // Show timestamp if it's been more than 5 minutes since the last message
        // or if it's from a different sender
        const showTimestamp =
          !prevMessage ||
          prevMessage.sent_from !== message.sent_from ||
          new Date(message.createdAt).getTime() -
            new Date(prevMessage.createdAt).getTime() >
            5 * 60 * 1000;

        return (
          <MessageItem
            key={message.id}
            message={message}
            isOwn={isOwn}
            showAvatar={showAvatar}
            showTimestamp={showTimestamp}
          />
        );
      })}

      {/* Scroll anchor */}
      <div ref={messagesEndRef} />
    </div>
  );
}

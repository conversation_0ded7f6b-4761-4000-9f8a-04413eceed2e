"use client";

import { useState, useEffect } from "react";
import { useParams, redirect } from "next/navigation";
import { useDocuments } from "@/hooks/useDocuments";
import { DocumentHeader } from "./header";
import { DocumentStatistics } from "./statistics";
import { DocumentTable } from "./table";
import { DocumentCreateDialog } from "./create-dialog";
import type { Document } from "@/lib/api/validators/schemas/document";
import type { Document as MockDocument } from "@/data/documents-mock";

// Adapter function to convert API document to UI document
const adaptApiDocumentToUI = (apiDocument: Document): MockDocument => ({
  id: apiDocument.id,
  name: apiDocument.name,
  path: apiDocument.path,
  file_type: apiDocument.file_type,
  size: apiDocument.size,
  status: apiDocument.status,
  category: apiDocument.category,
  association_entity: apiDocument.association_entity,
  association_id: apiDocument.association_id,
  proposalId: apiDocument.proposalId,
  createdAt: new Date(apiDocument.createdAt).toISOString(),
  updatedAt: new Date(apiDocument.updatedAt).toISOString(),
});

export function DocumentContainer() {
  const { slug } = useParams();

  const {
    documents,
    statistics,
    isLoading,
    isCreating,
    error,
    create,
    update,
    remove,
    clearError,
  } = useDocuments();

  const [isCreateDialogOpen, setIsCreateDialogOpen] = useState(false);

  useEffect(() => {
    if (!slug) {
      redirect("/");
    }
    // Data is automatically fetched by useSWR in the hook
    // No need for manual initialization
  }, [slug]);

  // useEffect(() => {
  //   if (error) {
  //     console.error("Document error:", error);
  //   }
  // }, [error]);

  // Clear error when component unmounts or when user interacts
  // useEffect(() => {
  //   return () => {
  //     clearError();
  //   };
  // }, [clearError]);

  const handleCreateDocument = async (documentData: FormData) => {
    try {
      await create(documentData);
      setIsCreateDialogOpen(false);
    } catch (error) {
      console.error("Failed to create document:", error);
    }
  };

  const handleUpdateDocument = async (id: string, documentData: any) => {
    try {
      await update(id, documentData);
    } catch (error) {
      console.error("Failed to update document:", error);
    }
  };

  const handleDeleteDocument = async (id: string) => {
    try {
      await remove(id);
    } catch (error) {
      console.error("Failed to delete document:", error);
    }
  };

  // Convert API documents to UI documents
  const uiDocuments = documents.map(adaptApiDocumentToUI);

  return (
    <div className="grid grid-cols-1 gap-12 p-4">
      <DocumentHeader
        onCreateDocument={() => setIsCreateDialogOpen(true)}
        documentsCount={documents.length}
      />

      <DocumentStatistics statistics={statistics} isLoading={isLoading} />

      <DocumentTable
        documents={uiDocuments}
        isLoading={isLoading}
        onUpdateDocument={handleUpdateDocument}
        onDeleteDocument={handleDeleteDocument}
      />

      <DocumentCreateDialog
        open={isCreateDialogOpen}
        onOpenChange={setIsCreateDialogOpen}
        onCreateDocument={handleCreateDocument}
        isLoading={isCreating}
      />
    </div>
  );
}

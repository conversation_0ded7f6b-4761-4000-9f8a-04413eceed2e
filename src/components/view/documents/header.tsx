"use client";

import { But<PERSON> } from "@/components/common/ui/button";
import { Plus } from "lucide-react";

interface DocumentHeaderProps {
  onCreateDocument: () => void;
  documentsCount: number;
}

export function DocumentHeader({
  onCreateDocument,
  documentsCount,
}: DocumentHeaderProps) {
  return (
    <div className="flex items-center justify-between">
      <div className="flex flex-col items-start gap-3">
        <h1 className="text-2xl font-bold">Documents</h1>
        <p className="text-sm text-gray-500">
          Manage your project documents and files
        </p>
      </div>

      <div className="flex flex-col items-end gap-4">
        <div className="text-sm text-gray-500">
          {documentsCount} {documentsCount === 1 ? "document" : "documents"}
        </div>
        <Button onClick={onCreateDocument} className="flex items-center gap-2">
          <Plus className="w-4 h-4" />
          Upload New Document
        </Button>
      </div>
    </div>
  );
}

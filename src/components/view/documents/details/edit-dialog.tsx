"use client";

import { useState, useEffect } from "react";
import { But<PERSON> } from "@/components/common/ui/button";
import { Input } from "@/components/common/ui/input";
import { Label } from "@/components/common/ui/label";
import {
  <PERSON><PERSON>,
  DialogContent,
  <PERSON><PERSON><PERSON>eader,
  <PERSON><PERSON>Tit<PERSON>,
  <PERSON><PERSON>Footer,
} from "@/components/common/ui/dialog";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/common/ui/select";
import type { Document, UpdateDocument } from "@/lib/api/validators/schemas/document";

interface DocumentEditDialogProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  document: Document;
  onUpdateDocument: (data: Partial<UpdateDocument>) => Promise<void>;
  isLoading: boolean;
}

export function DocumentEditDialog({
  open,
  onOpenChange,
  document,
  onUpdateDocument,
  isLoading,
}: DocumentEditDialogProps) {
  const [formData, setFormData] = useState({
    name: "",
    status: "",
    category: "",
    association_entity: "",
    association_id: "",
    proposalId: "",
  });

  // Initialize form data when document changes
  useEffect(() => {
    if (document) {
      setFormData({
        name: document.name,
        status: document.status,
        category: document.category,
        association_entity: document.association_entity,
        association_id: document.association_id,
        proposalId: document.proposalId || "",
      });
    }
  }, [document]);

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    try {
      await onUpdateDocument({
        name: formData.name,
        status: formData.status as Document["status"],
        category: formData.category,
        association_entity: formData.association_entity,
        association_id: formData.association_id,
        proposalId: formData.proposalId || undefined,
      });
    } catch (error) {
      console.error("Failed to update document:", error);
    }
  };

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="sm:max-w-[500px]">
        <DialogHeader>
          <DialogTitle>Edit Document</DialogTitle>
        </DialogHeader>
        
        <form onSubmit={handleSubmit} className="space-y-4">
          {/* Document Name */}
          <div className="space-y-2">
            <Label htmlFor="name">Document Name</Label>
            <Input
              id="name"
              value={formData.name}
              onChange={(e) => setFormData(prev => ({ ...prev, name: e.target.value }))}
              placeholder="Enter document name"
              required
            />
          </div>

          {/* Status */}
          <div className="space-y-2">
            <Label htmlFor="status">Status</Label>
            <Select
              value={formData.status}
              onValueChange={(value) => setFormData(prev => ({ ...prev, status: value }))}
            >
              <SelectTrigger>
                <SelectValue placeholder="Select status" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="created">Created</SelectItem>
                <SelectItem value="submitted">Submitted</SelectItem>
                <SelectItem value="received">Received</SelectItem>
                <SelectItem value="negotiating">Negotiating</SelectItem>
                <SelectItem value="agreed">Agreed</SelectItem>
                <SelectItem value="inprogress">In Progress</SelectItem>
                <SelectItem value="reviewing">Reviewing</SelectItem>
                <SelectItem value="completed">Completed</SelectItem>
              </SelectContent>
            </Select>
          </div>

          {/* Category */}
          <div className="space-y-2">
            <Label htmlFor="category">Category</Label>
            <Select
              value={formData.category}
              onValueChange={(value) => setFormData(prev => ({ ...prev, category: value }))}
            >
              <SelectTrigger>
                <SelectValue placeholder="Select category" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="requirements">Requirements</SelectItem>
                <SelectItem value="specifications">Specifications</SelectItem>
                <SelectItem value="design">Design</SelectItem>
                <SelectItem value="documentation">Documentation</SelectItem>
                <SelectItem value="legal">Legal</SelectItem>
                <SelectItem value="testing">Testing</SelectItem>
                <SelectItem value="other">Other</SelectItem>
              </SelectContent>
            </Select>
          </div>

          {/* Association Entity */}
          <div className="space-y-2">
            <Label htmlFor="association_entity">Association Entity</Label>
            <Select
              value={formData.association_entity}
              onValueChange={(value) => setFormData(prev => ({ ...prev, association_entity: value }))}
            >
              <SelectTrigger>
                <SelectValue placeholder="Select entity type" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="proposal">Proposal</SelectItem>
                <SelectItem value="contract">Contract</SelectItem>
                <SelectItem value="project">Project</SelectItem>
              </SelectContent>
            </Select>
          </div>

          {/* Association ID */}
          <div className="space-y-2">
            <Label htmlFor="association_id">Association ID</Label>
            <Input
              id="association_id"
              value={formData.association_id}
              onChange={(e) => setFormData(prev => ({ ...prev, association_id: e.target.value }))}
              placeholder="Enter associated entity ID"
              required
            />
          </div>

          {/* Proposal ID (Optional) */}
          <div className="space-y-2">
            <Label htmlFor="proposalId">Proposal ID (Optional)</Label>
            <Input
              id="proposalId"
              value={formData.proposalId}
              onChange={(e) => setFormData(prev => ({ ...prev, proposalId: e.target.value }))}
              placeholder="Enter proposal ID if applicable"
            />
          </div>

          <DialogFooter>
            <Button
              type="button"
              variant="outline"
              onClick={() => onOpenChange(false)}
            >
              Cancel
            </Button>
            <Button type="submit" disabled={isLoading}>
              {isLoading ? "Updating..." : "Update Document"}
            </Button>
          </DialogFooter>
        </form>
      </DialogContent>
    </Dialog>
  );
}

# Authentication Components

This directory contains authentication-related components built with ShadCN UI components and integrated with the Redux-based authentication system.

## Components

### SignIn Page (`/signin`)

A comprehensive sign-in page with the following features:

- **Social Authentication**: Google and LinkedIn OAuth integration
- **Email/Password Form**: Traditional credentials-based authentication (placeholder)
- **Form Validation**: Client-side validation with error handling
- **Loading States**: Visual feedback during authentication processes
- **Responsive Design**: Mobile-friendly layout using ShadCN components
- **Auto-redirect**: Automatically redirects authenticated users

#### Features:

- Password visibility toggle
- Form validation and error display
- Social provider buttons with React Icons (FaGoogle, FaLinkedin)
- Loading spinners and disabled states
- Links to signup and forgot password pages

### SignUp Page (`/signup`)

A registration page with extended functionality:

- **Social Registration**: Same OAuth providers as sign-in
- **Extended Form**: Name, email, password, and confirm password fields
- **Password Validation**: Minimum length and matching confirmation
- **Form Validation**: Comprehensive client-side validation
- **Consistent UI**: Matches sign-in page design patterns

#### Features:

- Dual password fields with individual visibility toggles
- Password strength validation
- Confirmation password matching
- Form state management
- Error handling and display

### AuthLoadingSpinner

A reusable loading component for authentication states:

- **Consistent Design**: Matches the auth page layout
- **Customizable Message**: Configurable loading text
- **Centered Layout**: Full-screen centered spinner
- **ShadCN Integration**: Uses Card components for consistency

## Integration with useAuth Hook

All components are fully integrated with the `useAuth` hook:

```typescript
const {
  login, // Function to authenticate with providers
  isAuthenticated, // Boolean authentication status
  isLoading, // Loading state for auth operations
  user, // Current user data
  logout, // Function to sign out
} = useAuth();
```

## Usage Examples

### Basic Sign-In Integration

```typescript
import { useAuth } from "@/hooks/useAuth";

function MyComponent() {
  const { login, isAuthenticated, isLoading } = useAuth();

  const handleGoogleSignIn = async () => {
    try {
      await login("google");
      // User will be redirected automatically
    } catch (error) {
      console.error("Sign in failed:", error);
    }
  };

  if (isLoading) return <AuthLoadingSpinner />;
  if (isAuthenticated) return <Dashboard />;

  return <SignInForm onGoogleSignIn={handleGoogleSignIn} />;
}
```

### Protected Route Pattern

```typescript
import { useAuth } from "@/hooks/useAuth";
import { useRouter } from "next/navigation";

function ProtectedPage() {
  const { isAuthenticated, isLoading } = useAuth();
  const router = useRouter();

  useEffect(() => {
    if (!isLoading && !isAuthenticated) {
      router.push("/signin");
    }
  }, [isAuthenticated, isLoading, router]);

  if (isLoading) return <AuthLoadingSpinner />;
  if (!isAuthenticated) return null;

  return <ProtectedContent />;
}
```

## ShadCN Components Used

- **Card**: Main container for auth forms
- **Button**: Primary and outline variants for actions
- **Input**: Form inputs with proper styling
- **Label**: Accessible form labels
- **Separator**: Visual divider between sections

## Styling and Theming

All components use:

- **CSS Variables**: Consistent with ShadCN theming system
- **Responsive Design**: Mobile-first approach
- **Dark Mode Support**: Automatic theme adaptation
- **Consistent Spacing**: Using Tailwind spacing utilities
- **Accessible Colors**: Proper contrast ratios

## Error Handling

Comprehensive error handling includes:

- **Network Errors**: Connection and timeout issues
- **Authentication Errors**: Invalid credentials or provider issues
- **Validation Errors**: Client-side form validation
- **User Feedback**: Clear error messages and visual indicators

## Security Considerations

- **CSRF Protection**: NextAuth.js built-in protection
- **Secure Cookies**: HTTP-only session cookies
- **OAuth Security**: Proper state validation
- **Input Sanitization**: Form input validation and sanitization

## Customization

Components can be customized by:

- **Modifying Styles**: Tailwind classes and CSS variables
- **Adding Providers**: Extending OAuth provider options
- **Custom Validation**: Adding business-specific validation rules
- **Branding**: Updating colors, logos, and messaging

## Accessibility

All components follow accessibility best practices:

- **Keyboard Navigation**: Full keyboard support
- **Screen Readers**: Proper ARIA labels and descriptions
- **Focus Management**: Logical tab order and focus indicators
- **Color Contrast**: WCAG compliant color combinations

import { <PERSON>, CardContent, CardHeader } from "@/components/common/ui/card";
import { But<PERSON> } from "@/components/common/ui/button";
import { Input } from "@/components/common/ui/input";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/common/ui/table";
import { Search, FileText } from "lucide-react";
import { IoFolderOpenOutline as OpenFileIcon } from "react-icons/io5";
import type { Proposal } from "@/data/proposals-mock";

interface ProposalTableProps {
  proposals: Proposal[];
  searchTerm: string;
  onSearchChange: (term: string) => void;
  isLoading: boolean;
  onView: (proposal: Proposal) => void;
  onEdit: (proposal: Proposal) => void;
}

export function ProposalTable({
  proposals,
  searchTerm,
  onSearchChange,
  isLoading,
  onView,
  onEdit,
}: ProposalTableProps) {
  const filteredProposals = proposals.filter(
    (proposal) =>
      proposal.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
      proposal.status.toLowerCase().includes(searchTerm.toLowerCase())
  );

  const getStatusColor = (status: string) => {
    switch (status) {
      case "approved":
        return "bg-green-100 text-green-800";
      case "pending":
        return "bg-yellow-100 text-yellow-800";
      case "rejected":
        return "bg-red-100 text-red-800";
      case "draft":
        return "bg-gray-100 text-gray-800";
      case "completed":
        return "bg-blue-100 text-blue-800";
      default:
        return "bg-gray-100 text-gray-800";
    }
  };

  if (isLoading) {
    return (
      <Card>
        <CardHeader>
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm text-muted-foreground">
                Loading proposals...
              </p>
            </div>
          </div>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            {[...Array(5)].map((_, index) => (
              <div
                key={index}
                className="h-16 bg-gray-200 rounded animate-pulse"
              />
            ))}
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <div className="space-y-6">
      {/* Search Bar */}
      <div className="flex flex-col sm:flex-row gap-4 items-start sm:items-center justify-between">
        <div className="relative flex-1 max-w-md">
          <Search className="absolute right-3 top-1/2 transform -translate-y-1/2 text-muted-foreground h-4 w-4" />
          <Input
            placeholder="Search proposals..."
            value={searchTerm}
            onChange={(e) => onSearchChange(e.target.value)}
            className="pl-10"
          />
        </div>
      </div>

      {/* Proposals Table */}
      <Card>
        <CardHeader>
          <div className="block">
            <p className="text-sm text-muted-foreground">
              {filteredProposals.length} of {proposals.length} proposals
            </p>
          </div>
        </CardHeader>
        <CardContent>
          <Table>
            <TableHeader>
              <TableRow>
                <TableHead>Proposal Name</TableHead>
                <TableHead>Status</TableHead>
                <TableHead>Budget Type</TableHead>
                <TableHead>Total Budget</TableHead>
                <TableHead>Duration</TableHead>
                <TableHead>Created Date</TableHead>
                <TableHead></TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {filteredProposals.map((proposal: any, index: number) => (
                <TableRow key={index}>
                  <TableCell>
                    <div>
                      <div className="font-medium text-foreground">
                        {proposal.name}
                      </div>
                      <div className="text-sm text-muted-foreground truncate max-w-xs">
                        {proposal.description}
                      </div>
                    </div>
                  </TableCell>
                  <TableCell>
                    <span
                      className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${getStatusColor(
                        proposal.status
                      )}`}
                    >
                      {proposal.status.charAt(0).toUpperCase() +
                        proposal.status.slice(1)}
                    </span>
                  </TableCell>
                  <TableCell>
                    <span className="text-sm text-foreground capitalize">
                      {proposal.budgetType}
                    </span>
                  </TableCell>
                  <TableCell>
                    <span className="text-sm font-medium text-foreground">
                      ${proposal.totalBudget.toLocaleString()}
                    </span>
                  </TableCell>
                  <TableCell>
                    <span className="text-sm text-foreground">
                      {proposal.duration} weeks
                    </span>
                  </TableCell>
                  <TableCell>
                    <span className="text-sm text-foreground">
                      {new Date(proposal.createdDate).toLocaleDateString()}
                    </span>
                  </TableCell>
                  <TableCell>
                    <Button
                      variant="ghost"
                      size="sm"
                      onClick={() => onView(proposal)}
                    >
                      <OpenFileIcon />
                      Open
                    </Button>
                  </TableCell>
                </TableRow>
              ))}
            </TableBody>
          </Table>

          {filteredProposals.length === 0 && (
            <div className="flex flex-col text-center py-8">
              <FileText className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
              <h3 className="text-lg font-medium text-foreground mb-2">
                No proposals found
              </h3>
              <p className="text-muted-foreground">
                {searchTerm
                  ? "Try adjusting your search terms"
                  : "Get started by creating your first proposal"}
              </p>
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  );
}

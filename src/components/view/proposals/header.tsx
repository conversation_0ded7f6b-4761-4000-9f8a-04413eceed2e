import { date } from "zod";

interface ProposalHeaderProps {
  title: string;
  subtitle: string;
}

export function ProposalHeader({ title, subtitle }: ProposalHeaderProps) {
  const today = new Date().toLocaleDateString();

  return (
    <div className="flex flex-row justify-between items-center gap-4">
      <h1 className="text-3xl font-bold text-foreground">{title}</h1>
      <div className="flex flex-col items-end gap-2">
        <p className="text-muted-foreground mt-2">{subtitle}</p>
        <p className="text-muted-foreground mt-2">{today}</p>
      </div>
    </div>
  );
}

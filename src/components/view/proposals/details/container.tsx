"use client";

import { useState, useEffect } from "react";
import { api } from "@/lib/common/requests";
import { useProposal } from "@/hooks/useProposal";
import { ProposalDetailsHeader } from "./header";
import { ProposalDetailsContent } from "./content";

import { ProposalEditDialog } from "./edit-dialog";
import { ProposalDeleteDialog } from "./delete-dialog";
import { ProposalReminderDialog } from "./reminder-dialog";
import { Card, CardContent } from "@/components/common/ui/card";
import { Skeleton } from "@/components/common/ui/skeleton";
import { toast } from "sonner";
import type { Proposal } from "@/lib/api/validators/schemas/proposal";

interface ProposalDetailsContainerProps {
  proposalId: string;
}

export function ProposalDetailsContainer({
  proposalId,
}: ProposalDetailsContainerProps) {
  const {
    currentProposal,
    isLoading,
    isUpdating,
    isDeleting,
    error,
    fetchById,
    update,
    remove,
    clearError,
  } = useProposal();

  const [isEditDialogOpen, setIsEditDialogOpen] = useState(false);
  const [isDeleteDialogOpen, setIsDeleteDialogOpen] = useState(false);
  const [isReminderDialogOpen, setIsReminderDialogOpen] = useState(false);

  // Fetch proposal details on mount
  useEffect(() => {
    if (proposalId) {
      fetchById(proposalId);
    }
  }, [proposalId, fetchById]);

  // Clear errors when component unmounts
  useEffect(() => {
    return () => {
      if (error) {
        clearError();
      }
    };
  }, [error, clearError]);

  const handleEdit = () => {
    setIsEditDialogOpen(true);
  };

  const handleDelete = () => {
    setIsDeleteDialogOpen(true);
  };

  const handleSendReminder = () => {
    setIsReminderDialogOpen(true);
  };

  const handleEditSubmit = async (updatedData: Partial<Proposal>) => {
    if (!currentProposal) return;

    try {
      await update({
        id: currentProposal.id,
        ...updatedData,
      });
      setIsEditDialogOpen(false);
      toast.success("Proposal updated successfully");
    } catch (error) {
      console.error("Failed to update proposal:", error);
    }
  };

  const handleDeleteConfirm = async () => {
    if (!currentProposal) return;

    try {
      await remove(currentProposal.id);
      setIsDeleteDialogOpen(false);
      toast.success("Proposal deleted successfully");
      // Redirect to proposals list
      window.history.back();
    } catch (error) {
      console.error("Failed to delete proposal:", error);
    }
  };

  const handleReminderSubmit = async (reminderData: {
    message: string;
    scheduledDate: Date;
  }) => {
    if (!currentProposal) return;

    try {
      const result = await api.post("proposal/reminder", {
        proposalId: currentProposal.id,
        message: reminderData.message,
        scheduledDate: reminderData.scheduledDate.toISOString(),
      });

      if (result.error) {
        throw new Error(result.message || "Failed to schedule reminder");
      }

      setIsReminderDialogOpen(false);
      toast.success(result.message || "Reminder scheduled successfully");
    } catch (error) {
      console.error("Failed to send reminder:", error);
      toast.error(
        error instanceof Error ? error.message : "Failed to send reminder"
      );
    }
  };

  // Loading state
  if (isLoading) {
    return (
      <div className="space-y-6 p-4">
        <div className="flex justify-between items-start">
          <div className="space-y-2">
            <Skeleton className="h-8 w-64" />
            <Skeleton className="h-4 w-96" />
          </div>
          <div className="flex gap-2">
            <Skeleton className="h-10 w-20" />
            <Skeleton className="h-10 w-20" />
            <Skeleton className="h-10 w-24" />
          </div>
        </div>
        <Card>
          <CardContent className="p-6 space-y-4">
            <Skeleton className="h-4 w-full" />
            <Skeleton className="h-4 w-3/4" />
            <Skeleton className="h-4 w-1/2" />
          </CardContent>
        </Card>
      </div>
    );
  }

  // Error state
  if (error && !currentProposal) {
    return (
      <div className="space-y-4 p-4">
        <div className="bg-destructive/10 border border-destructive/20 text-destructive px-4 py-3 rounded-md">
          <p className="text-sm">{error}</p>
          <button
            onClick={clearError}
            className="text-destructive hover:text-destructive/80 text-sm underline ml-2"
          >
            Dismiss
          </button>
        </div>
      </div>
    );
  }

  // No proposal found
  if (!currentProposal) {
    return (
      <div className="space-y-4 p-4">
        <div className="text-center py-12">
          <h2 className="text-xl font-semibold text-muted-foreground">
            Proposal not found
          </h2>
          <p className="text-sm text-muted-foreground mt-2">
            The proposal you&apos;re looking for doesn&apos;t exist or you
            don&apos;t have access to it.
          </p>
        </div>
      </div>
    );
  }

  return (
    <div className="grid grid-cols-1 gap-12 p-4">
      {/* Error Display */}
      {error && (
        <div className="bg-destructive/10 border border-destructive/20 text-destructive px-4 py-3 rounded-md">
          <p className="text-sm">{error}</p>
          <button
            onClick={clearError}
            className="text-destructive hover:text-destructive/80 text-sm underline ml-2"
          >
            Dismiss
          </button>
        </div>
      )}

      {/* Header with Actions */}
      <ProposalDetailsHeader
        proposal={currentProposal}
        onEdit={handleEdit}
        onDelete={handleDelete}
        onSendReminder={handleSendReminder}
        isUpdating={isUpdating}
        isDeleting={isDeleting}
      />

      {/* Main Content */}
      <ProposalDetailsContent proposal={currentProposal} />

      {/* Action Dialogs */}
      <ProposalEditDialog
        proposal={currentProposal}
        isOpen={isEditDialogOpen}
        onOpenChange={setIsEditDialogOpen}
        onSubmit={handleEditSubmit}
        isUpdating={isUpdating}
      />

      <ProposalDeleteDialog
        proposal={currentProposal}
        isOpen={isDeleteDialogOpen}
        onOpenChange={setIsDeleteDialogOpen}
        onConfirm={handleDeleteConfirm}
        isDeleting={isDeleting}
      />

      <ProposalReminderDialog
        proposal={currentProposal}
        isOpen={isReminderDialogOpen}
        onOpenChange={setIsReminderDialogOpen}
        onSubmit={handleReminderSubmit}
      />
    </div>
  );
}

import { Card, CardContent } from "@/components/common/ui/card";
import { FileText, Calendar, Users, DollarSign } from "lucide-react";
import type { ProposalStats } from "@/data/proposals-mock";

interface ProposalStatisticsProps {
  statistics: ProposalStats | null;
  isLoading: boolean;
}

export function ProposalStatistics({
  statistics,
  isLoading,
}: ProposalStatisticsProps) {
  if (isLoading) {
    return (
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-5 gap-4">
        {[...Array(5)].map((_, index) => (
          <Card key={index}>
            <CardContent className="p-6">
              <div className="flex items-center">
                <div className="h-8 w-8 bg-muted rounded animate-pulse" />
                <div className="ml-4 space-y-2">
                  <div className="h-4 w-20 bg-muted rounded animate-pulse" />
                  <div className="h-6 w-12 bg-muted rounded animate-pulse" />
                </div>
              </div>
            </CardContent>
          </Card>
        ))}
      </div>
    );
  }

  if (!statistics) {
    return (
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-5 gap-4">
        <Card>
          <CardContent className="p-6">
            <div className="text-center text-muted-foreground">
              <FileText className="h-8 w-8 mx-auto mb-2" />
              <p className="text-sm">No statistics available</p>
            </div>
          </CardContent>
        </Card>
      </div>
    );
  }

  const statCards = [
    {
      title: "Total Proposals",
      value: statistics.total,
      icon: FileText,
      color: "text-blue-600",
    },
    {
      title: "Pending",
      value: statistics.pending,
      icon: Calendar,
      color: "text-yellow-600",
    },
    {
      title: "Approved",
      value: statistics.approved,
      icon: Users,
      color: "text-green-600",
    },
    {
      title: "Rejected",
      value: statistics.rejected,
      icon: FileText,
      color: "text-red-600",
    },
    {
      title: "Total Value",
      value: `$${statistics.totalValue.toLocaleString()}`,
      icon: DollarSign,
      color: "text-purple-600",
    },
  ];

  return (
    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-5 gap-4">
      {statCards.map((stat, index) => {
        const IconComponent = stat.icon;
        return (
          <Card key={index}>
            <CardContent className="px-6">
              <div className="flex flex-col items-start gap-3">
                <IconComponent className={`h-8 w-8 ${stat.color}`} />
                <div className="flex flex-col">
                  <p className="text-sm font-medium text-muted-foreground">
                    {stat.title}
                  </p>
                  <p className="text-2xl font-bold text-foreground">
                    {stat.value}
                  </p>
                </div>
              </div>
            </CardContent>
          </Card>
        );
      })}
    </div>
  );
}

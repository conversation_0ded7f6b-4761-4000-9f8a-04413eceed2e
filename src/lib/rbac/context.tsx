"use client";

/**
 * RBAC Context and Provider
 *
 * React context for managing RBAC state and providing access to RBAC functionality
 * throughout the application.
 */

import React, {
  createContext,
  useContext,
  useEffect,
  useCallback,
} from "react";
import { useSession } from "next-auth/react";
import { useSelector, useDispatch } from "react-redux";
import { AppDispatch } from "@/store";
import { useAuth } from "@/hooks/useAuth";
import { fetchUsers } from "@/store/actions/rbac";
import {
  selectUsers,
  selectRBACLoading,
  selectRBACError,
} from "@/store/slices/rbac";
import { useRBACRedux } from "./hooks";
import { createPermissionId, parsePermissionId } from "@/lib/rbac";

interface RBACContextValue {
  // User data
  currentUser: any;
  users: any[];
  roles: any[];

  // Legacy permission checking (backward compatibility)
  hasPermission: (permission: string) => boolean;
  hasAnyPermission: (permissions: string[]) => boolean;
  hasAllPermissions: (permissions: string[]) => boolean;

  // New entity-based permission checking
  hasEntityPermission: (entity: string, action: string) => boolean;
  hasAnyEntityPermission: (entity: string, actions: string[]) => boolean;
  hasAllEntityPermissions: (entity: string, actions: string[]) => boolean;

  // Loading states
  isLoading: boolean;
  error?: string;

  // Actions
  initializeRBAC: () => void;
  refreshData: () => void;
}

const RBACContext = createContext<RBACContextValue | null>(null);

interface RBACProviderProps {
  children: React.ReactNode;
}

export function RBACProvider({ children }: RBACProviderProps) {
  const { data: session, status } = useSession();
  const dispatch = useDispatch<AppDispatch>();
  const { user: currentUser } = useAuth(); // Get current user from auth state

  // Redux selectors
  const { users, roles } = useRBACRedux();

  const isLoading = useSelector(selectRBACLoading);
  const error = useSelector(selectRBACError);

  // Initialize RBAC data
  const initializeRBAC = useCallback(() => {
    if (session?.user?.id) {
      // Current user data comes from auth state, only fetch users
      dispatch(fetchUsers());
    }
  }, [dispatch, session?.user?.id]);

  // Refresh all data
  const refreshData = useCallback(() => {
    initializeRBAC();
  }, [initializeRBAC]);

  // Initialize on mount and session change
  useEffect(() => {
    if (status !== "loading" && session?.user?.id) {
      initializeRBAC();
    }
  }, [status, session?.user?.id, initializeRBAC]);

  // Entity-based permission checking functions
  const hasEntityPermission = useCallback(
    (entity: string, action: string): boolean => {
      if (!currentUser?.role?.permissions) return false;
      const entityPermissions = (currentUser.role.permissions as any)[entity];
      return entityPermissions ? entityPermissions.includes(action) : false;
    },
    [currentUser]
  );

  // Legacy string-based permission checking (backward compatibility)
  const hasPermission = useCallback(
    (permission: string): boolean => {
      if (!currentUser?.role?.permissions) return false;

      // Try to parse as entity:action format
      const parsed = parsePermissionId(permission);
      if (parsed) {
        return hasEntityPermission(parsed.entity, parsed.action);
      }

      // Fallback: check if permissions is still array format (legacy)
      if (Array.isArray(currentUser.role.permissions)) {
        return currentUser.role.permissions.includes(permission);
      }

      return false;
    },
    [currentUser, hasEntityPermission]
  );

  const hasAnyPermission = useCallback(
    (permissions: string[]): boolean => {
      if (!currentUser?.role?.permissions) return false;
      return permissions.some((permission) => hasPermission(permission));
    },
    [currentUser, hasPermission]
  );

  const hasAllPermissions = useCallback(
    (permissions: string[]): boolean => {
      if (!currentUser?.role?.permissions) return false;
      return permissions.every((permission) => hasPermission(permission));
    },
    [currentUser, hasPermission]
  );

  // Entity-based permission checking for multiple permissions
  const hasAnyEntityPermission = useCallback(
    (entity: string, actions: string[]): boolean => {
      return actions.some((action) => hasEntityPermission(entity, action));
    },
    [hasEntityPermission]
  );

  const hasAllEntityPermissions = useCallback(
    (entity: string, actions: string[]): boolean => {
      return actions.every((action) => hasEntityPermission(entity, action));
    },
    [hasEntityPermission]
  );

  const contextValue: RBACContextValue = {
    currentUser,
    users,
    roles,
    // Legacy functions
    hasPermission,
    hasAnyPermission,
    hasAllPermissions,
    // New entity-based functions
    hasEntityPermission,
    hasAnyEntityPermission,
    hasAllEntityPermissions,
    isLoading,
    error,
    initializeRBAC,
    refreshData,
  };

  return (
    <RBACContext.Provider value={contextValue}>{children}</RBACContext.Provider>
  );
}

// Hook to use RBAC context
export function useRBAC(): RBACContextValue {
  const context = useContext(RBACContext);
  if (!context) {
    throw new Error("useRBAC must be used within an RBACProvider");
  }
  return context;
}

// Export usePermission hook from hooks file for backward compatibility
export { usePermissions as usePermission } from "@/hooks/useRBAC";

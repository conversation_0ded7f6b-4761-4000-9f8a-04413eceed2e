/**
 * RBAC Library Index
 *
 * Main entry point for the RBAC (Role-Based Access Control) library.
 * Exports all types, services, and utilities.
 */

// Export all types
export * from "./types";

// Export core service
export { RBACService } from "./service";

// Export context and hooks
export { RBACProvider, useRBAC, usePermission } from "./context";
export {
  useRBACPermissions,
  useRBACManagement,
  useMultiplePermissions,
  useEntityPermissions,
} from "@/hooks/useRBAC";

// Export default entities for common use cases
export const DEFAULT_ENTITIES = {
  USER: "user",
  ROLE: "role",
  PERMISSION: "permission",
  PROJECT: "project",
  DOCUMENT: "document",
  CONTRACT: "contract",
  PROPOSAL: "proposal",
  CLIENT: "client",
  PRODUCT: "product",
  CATEGORY: "category",
  MEDIA: "media",
} as const;

// Export permission actions
export const PERMISSION_ACTIONS = {
  CREATE: "create",
  READ: "read",
  UPDATE: "update",
  DELETE: "delete",
} as const;

// Export common role names
export const DEFAULT_ROLES = {
  SUPER_ADMIN: "super_admin",
  ADMIN: "admin",
  MANAGER: "manager",
  EDITOR: "editor",
  VIEWER: "viewer",
  USER: "user",
} as const;

// Utility function to create permission identifier
export function createPermissionId(entity: string, action: string): string {
  return `${entity}:${action}`;
}

// Utility function to parse permission identifier
export function parsePermissionId(
  permissionId: string
): { entity: string; action: string } | null {
  const parts = permissionId.split(":");
  if (parts.length !== 2) return null;

  return {
    entity: parts[0],
    action: parts[1],
  };
}

// Utility function to check if a role is a system role
export function isSystemRole(roleName: string): boolean {
  return Object.values(DEFAULT_ROLES).includes(roleName as any);
}

// Utility function to get all permissions for an entity
export function getEntityPermissions(entity: string) {
  return Object.values(PERMISSION_ACTIONS).map((action) => ({
    entity,
    action,
    id: createPermissionId(entity, action),
  }));
}

// Utility function to check entity permission in object format
export function checkEntityPermission(
  permissions: Record<string, string[]> | string[],
  entity: string,
  action: string
): boolean {
  // Handle legacy array format
  if (Array.isArray(permissions)) {
    return permissions.includes(createPermissionId(entity, action));
  }

  // Handle new entity-based object format
  const entityPermissions = permissions[entity];
  return entityPermissions ? entityPermissions.includes(action) : false;
}

// Utility function to convert legacy permissions to entity-based format
export function convertLegacyPermissions(
  legacyPermissions: string[]
): Record<string, string[]> {
  const entityPermissions: Record<string, string[]> = {};

  legacyPermissions.forEach((permission) => {
    const parsed = parsePermissionId(permission);
    if (parsed) {
      if (!entityPermissions[parsed.entity]) {
        entityPermissions[parsed.entity] = [];
      }
      if (!entityPermissions[parsed.entity].includes(parsed.action)) {
        entityPermissions[parsed.entity].push(parsed.action);
      }
    }
  });

  return entityPermissions;
}

// Utility function to convert entity-based permissions to legacy format
export function convertToLegacyPermissions(
  entityPermissions: Record<string, string[]>
): string[] {
  const legacyPermissions: string[] = [];

  Object.entries(entityPermissions).forEach(([entity, actions]) => {
    actions.forEach((action) => {
      legacyPermissions.push(createPermissionId(entity, action));
    });
  });

  return legacyPermissions;
}

// Utility function to merge permissions from multiple roles
export function mergeRolePermissions(
  rolePermissions: Array<Record<string, string[]>>
): Record<string, string[]> {
  const merged: Record<string, string[]> = {};

  rolePermissions.forEach((permissions) => {
    Object.entries(permissions).forEach(([entity, actions]) => {
      if (!merged[entity]) {
        merged[entity] = [];
      }
      actions.forEach((action) => {
        if (!merged[entity].includes(action)) {
          merged[entity].push(action);
        }
      });
    });
  });

  return merged;
}

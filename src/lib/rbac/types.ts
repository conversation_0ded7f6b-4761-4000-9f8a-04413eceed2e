/**
 * RBAC (Role-Based Access Control) Types and Interfaces
 *
 * This module defines the core types and interfaces for the RBAC system,
 * including roles, permissions, users, and entities.
 */

// Core permission actions for any entity
export type PermissionAction = "create" | "read" | "update" | "delete";

// Entity types that can have permissions applied
export type EntityType = string;

// Entity-based permissions structure: { [entity]: [actions] }
export type EntityPermissions = Record<string, PermissionAction[]>;

// Legacy permission definition for backward compatibility
export interface Permission {
  id: string;
  entity: EntityType;
  action: PermissionAction;
  description?: string;
  createdAt: Date;
  updatedAt: Date;
}

// Role definition with JSON-based permissions
export interface Role {
  id: string;
  name: string;
  description?: string;
  permissions: EntityPermissions; // New JSON-based structure
  status?: "active" | "inactive" | "created";
  isSystem?: boolean; // System roles cannot be deleted
  createdAt: Date;
  updatedAt: Date;
}

// Legacy role interface for backward compatibility
export interface LegacyRole {
  id: string;
  name: string;
  description?: string;
  permissions: Permission[]; // Old array-based structure
  isSystem?: boolean;
  createdAt: Date;
  updatedAt: Date;
}

// User with assigned roles
export interface RBACUser {
  id: string;
  name?: string;
  email: string;
  roles: Role[];
  isActive: boolean;
  createdAt: Date;
  updatedAt: Date;
}

// Permission check context
export interface PermissionContext {
  userId: string;
  entity: EntityType;
  action: PermissionAction;
  resourceId?: string; // Optional resource-specific permission
}

// Permission check result
export interface PermissionResult {
  granted: boolean;
  reason?: string;
  matchedPermissions?: Permission[];
}

// RBAC service configuration
export interface RBACConfig {
  enableLogging?: boolean;
  cachePermissions?: boolean;
  cacheTTL?: number; // Cache time-to-live in milliseconds
  defaultRoles?: string[]; // Default roles for new users
}

// User-Role assignment
export interface UserRoleAssignment {
  userId: string;
  roleId: string;
  assignedBy: string;
  assignedAt: Date;
  expiresAt?: Date;
}

// Role-Permission assignment
export interface RolePermissionAssignment {
  roleId: string;
  permissionId: string;
  assignedBy: string;
  assignedAt: Date;
}

// RBAC operation results
export interface RBACOperationResult<T = any> {
  success: boolean;
  data?: T;
  error?: string;
  code?: string;
}

// Entity permission matrix for UI display
export interface EntityPermissionMatrix {
  entity: EntityType;
  permissions: {
    [K in PermissionAction]: boolean;
  };
}

// Role permission matrix for management UI
export interface RolePermissionMatrix {
  roleId: string;
  roleName: string;
  entities: EntityPermissionMatrix[];
}

// User role assignment data for UI
export interface UserRoleAssignmentData {
  userId: string;
  userName: string;
  userEmail: string;
  roles: {
    roleId: string;
    roleName: string;
    assignedAt: Date;
    expiresAt?: Date;
  }[];
}

// RBAC events for logging and auditing
export type RBACEventType =
  | "role_created"
  | "role_updated"
  | "role_deleted"
  | "permission_created"
  | "permission_updated"
  | "permission_deleted"
  | "user_role_assigned"
  | "user_role_revoked"
  | "role_permission_assigned"
  | "role_permission_revoked"
  | "permission_check";

export interface RBACEvent {
  id: string;
  type: RBACEventType;
  userId: string;
  targetUserId?: string;
  roleId?: string;
  permissionId?: string;
  entity?: EntityType;
  action?: PermissionAction;
  metadata?: Record<string, any>;
  timestamp: Date;
}

// Component wrapper props
export interface RBACWrapperProps {
  entity: EntityType;
  action: PermissionAction;
  resourceId?: string;
  fallback?: React.ReactNode;
  children: React.ReactNode;
  requireAll?: boolean; // If multiple permissions, require all vs any
}

// Hook return types
export interface UseRBACReturn {
  hasPermission: (
    entity: EntityType,
    action: PermissionAction,
    resourceId?: string
  ) => boolean;
  checkPermission: (context: PermissionContext) => Promise<PermissionResult>;
  userRoles: Role[];
  userPermissions: Permission[];
  isLoading: boolean;
  error?: string;
}

export interface UseRBACManagementReturn {
  // User management
  users: RBACUser[];
  assignRoleToUser: (
    userId: string,
    roleId: string
  ) => Promise<RBACOperationResult>;
  revokeRoleFromUser: (
    userId: string,
    roleId: string
  ) => Promise<RBACOperationResult>;

  // Role management
  roles: Role[];
  createRole: (
    name: string,
    description?: string
  ) => Promise<RBACOperationResult<Role>>;
  updateRole: (
    roleId: string,
    updates: Partial<Role>
  ) => Promise<RBACOperationResult<Role>>;
  deleteRole: (roleId: string) => Promise<RBACOperationResult>;

  // Permission management
  permissions: Permission[];
  createPermission: (
    entity: EntityType,
    action: PermissionAction,
    description?: string
  ) => Promise<RBACOperationResult<Permission>>;
  assignPermissionToRole: (
    roleId: string,
    permissionId: string
  ) => Promise<RBACOperationResult>;
  revokePermissionFromRole: (
    roleId: string,
    permissionId: string
  ) => Promise<RBACOperationResult>;

  // Loading states
  isLoading: boolean;
  error?: string;

  // Refresh data
  refresh: () => Promise<void>;
}

"use client";

/**
 * <PERSON><PERSON> Hooks
 *
 * Re-exports hooks from the main hooks location for backward compatibility.
 * All hooks have been consolidated to @/hooks/useRBAC for better organization.
 */

// Re-export all hooks from the main location
export {
  useRBAC,
  useRBACRedux,
  useUsersByRole,
  useRole,
  usePermissions,
  useUserProfile,
  useRoleManagement,
  useUserManagement,
  useMultiplePermissions,
  useEntityPermissions,
} from "@/hooks/useRBAC";

// Keep backward compatibility
export { useUserProfile as useProfile } from "@/hooks/useRBAC";

// Keep the original hook names for backward compatibility
export {
  usePermissions as useRBACPermissions,
  useRoleManagement as useRBACManagement,
} from "@/hooks/useRBAC";

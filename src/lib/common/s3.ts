"use server";

import {
  S3Client,
  GetObjectCommand,
  PutObjectCommand,
} from "@aws-sdk/client-s3";
import { getSignedUrl } from "@aws-sdk/s3-request-presigner";

// Create an S3 client
//
// You must copy the endpoint from your B2 bucket details
// and set the region to match.
const region = process.env.B2_REGION!;
const bucket = process.env.B2_BUCKET!;
const accessKeyId = process.env.B2_KEY_ID!;
const secretAccessKey = process.env.B2_APP_KEY!;

const client = new S3Client({
  endpoint: `https://s3.${region}.backblazeb2.com`,
  region: region,
  credentials: {
    accessKeyId,
    secretAccessKey,
  },
  forcePathStyle: true,
});

export async function UploadToS3(location: string, file: File) {
  const key = location + "/" + file.name;

  const buffer = await file.arrayBuffer();
  const uint8Array = new Uint8Array(buffer);

  try {
    await client.send(
      new PutObjectCommand({
        Bucket: bucket,
        Key: key,
        Body: uint8Array,
        ContentType: file.type,
      })
    );

    return key;
  } catch (err) {
    console.log("Error: ", err);
  }
}

export async function getSignedURL(key: string) {
  try {
    const command = new GetObjectCommand({
      Bucket: bucket,
      Key: key,
    });
    const url = await getSignedUrl(client, command, {
      expiresIn: 3600,
    });

    console.log(url);

    return url;
  } catch (err) {
    console.log("Error: ", err);
  }
}

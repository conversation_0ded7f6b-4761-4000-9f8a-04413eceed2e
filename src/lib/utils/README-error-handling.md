# Error Handling Implementation Guide

This document outlines the new error handling pattern implemented across the application using Sonner for notifications and standardized response objects.

## 🎯 Overview

The new error handling system provides:
- **Consistent API responses** using ServiceResponse interface
- **Automatic notifications** using Sonner toast library
- **Centralized error management** in Redux actions
- **Clean component code** without manual error handling

## 🏗️ Architecture

### 1. Server-Side Services

Services now return standardized `ServiceResponse` objects instead of throwing errors:

```typescript
// ✅ Good - Return ServiceResponse
async login(email: string, password: string): Promise<ServiceResponse> {
  try {
    // ... validation logic
    if (!user) {
      return this.createErrorResponse("Invalid email or password", 401);
    }
    return this.createSuccessResponse(user, 200);
  } catch (error) {
    return this.createErrorResponse("An unexpected error occurred", 500);
  }
}

// ❌ Bad - Throwing errors
async login(email: string, password: string) {
  if (!user) {
    throw new Error("Invalid email or password"); // Don't do this
  }
}
```

### 2. Redux Actions

Redux actions handle the response objects and show appropriate notifications:

```typescript
export const loginUser = createAsyncThunk(
  "auth/loginUser",
  async (credentials, { dispatch, rejectWithValue }) => {
    try {
      const result = await api.post("auth/login", credentials);
      
      if (result.success) {
        toast.success("Login successful!");
        return result;
      } else {
        toast.error(result.error || "Login failed");
        return rejectWithValue(result);
      }
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : "Login failed";
      toast.error(errorMessage);
      return rejectWithValue({ error: errorMessage });
    }
  }
);
```

### 3. Component Integration

Components can now focus on business logic without manual error handling:

```typescript
const handleLogin = async (email: string, password: string) => {
  setIsSubmitting(true);
  
  try {
    await dispatch(loginUser({ email, password }));
    // Success notification is handled by Redux action
  } catch (error) {
    // Error notification is handled by Redux action
    console.error("Login error:", error);
  } finally {
    setIsSubmitting(false);
  }
};
```

## 📋 Implementation Checklist

### For Services (`src/lib/api/services/`)

- [ ] Return `ServiceResponse<T>` objects instead of throwing errors
- [ ] Use `this.createSuccessResponse(data, statusCode)` for success
- [ ] Use `this.createErrorResponse(error, statusCode)` for errors
- [ ] Wrap operations in try-catch blocks
- [ ] Use appropriate HTTP status codes (400, 401, 403, 404, 500, etc.)

### For Redux Actions (`src/store/actions/`)

- [ ] Import `toast` from `sonner`
- [ ] Use `rejectWithValue` for error handling
- [ ] Show success notifications with `toast.success()`
- [ ] Show error notifications with `toast.error()`
- [ ] Handle both API response errors and caught exceptions

### For Components

- [ ] Remove manual error state management where Redux handles it
- [ ] Remove manual toast notifications
- [ ] Keep try-catch for logging purposes
- [ ] Focus on business logic and UI state

## 🔧 ServiceResponse Interface

```typescript
interface ServiceResponse<T = any> {
  success: boolean;
  data?: T;
  error?: string;
  errors?: ValidationError[];
  statusCode?: number;
  timestamp?: string;
}
```

## 🎨 Toast Notification Types

```typescript
// Success notifications
toast.success("Operation completed successfully!");

// Error notifications
toast.error("Something went wrong");

// Info notifications
toast.info("Processing your request...");

// Warning notifications
toast.warning("Please check your input");
```

## 📝 Examples

### Auth Service Example

```typescript
async register(name: string, email: string, password: string): Promise<ServiceResponse> {
  try {
    if (!name) {
      return this.createErrorResponse("Name is required", 400);
    }
    
    const existingUser = await this.findUser(email);
    if (existingUser) {
      return this.createErrorResponse("User already exists", 409);
    }
    
    const user = await this.createUser({ name, email, password });
    return this.createSuccessResponse(user, 201);
  } catch (error) {
    return this.createErrorResponse("Registration failed", 500);
  }
}
```

### Redux Action Example

```typescript
export const registerUser = createAsyncThunk(
  "auth/register",
  async (userData, { dispatch, rejectWithValue }) => {
    try {
      const result = await api.post("auth/register", userData);
      
      if (result.success) {
        toast.success("Registration successful! Please sign in.");
        return result;
      } else {
        toast.error(result.error || "Registration failed");
        return rejectWithValue(result);
      }
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : "Registration failed";
      toast.error(errorMessage);
      return rejectWithValue({ error: errorMessage });
    }
  }
);
```

### Component Example

```typescript
const handleSubmit = async (formData) => {
  setIsLoading(true);
  
  try {
    await dispatch(registerUser(formData));
    // Success handling is automatic via Redux action
  } catch (error) {
    // Error handling is automatic via Redux action
    console.error("Registration error:", error);
  } finally {
    setIsLoading(false);
  }
};
```

## 🚀 Benefits

1. **Consistency**: All errors are handled the same way across the app
2. **User Experience**: Users get immediate feedback via toast notifications
3. **Developer Experience**: Less boilerplate code in components
4. **Maintainability**: Centralized error handling logic
5. **Debugging**: Better error logging and tracking

## 🔍 Migration Guide

To migrate existing code:

1. **Services**: Replace `throw new Error()` with `return this.createErrorResponse()`
2. **Actions**: Add toast notifications and use `rejectWithValue`
3. **Components**: Remove manual error handling and toast calls
4. **Test**: Verify notifications appear correctly

This pattern ensures consistent, user-friendly error handling throughout the application while keeping the code clean and maintainable.

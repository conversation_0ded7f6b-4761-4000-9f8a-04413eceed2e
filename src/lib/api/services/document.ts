import { z } from "zod";
import { BaseService } from "./base";
import {
  CreateDocumentSchema,
  type Document,
  type CreateDocument,
  type UpdateDocument,
} from "../validators/schemas/document";
import { UploadToS3, getSignedURL } from "@/lib/common/s3";
import { prisma } from "@/lib/common/prisma";
import { DatabaseOptions } from "./base";

export class DocumentService extends BaseService {
  private authRequired: boolean;

  constructor(config: any = {}) {
    super(config);
    this.authRequired = config.requireAuth ?? true;
    // Set the Prisma model for database operations
    this.setModel(prisma.document);
  }

  /**
   * Get all documents
   */
  async getDocuments(): Promise<any> {
    return this.executeOperation(async () => {
      // Require authentication if enabled
      if (this.authRequired) {
        this.requireAuth();
      }

      const options: DatabaseOptions = {
        select: {
          id: true,
          name: true,
          path: true,
          file_type: true,
          size: true,
          status: true,
          category: true,
          association_entity: true,
          association_id: true,
          createdAt: true,
          updatedAt: true,
          proposal: true,
          contract: true,
          message: true,
        },
        orderBy: {
          createdAt: "desc",
        },
      };

      // Fetch documents from database using BaseService method
      let documents = await this.findManyRecords<Document>(options);

      function getSignedURLs(documents: any[]) {
        return Promise.all(
          documents.map(async (document: any) => ({
            ...document,
            path: await getSignedURL(document.path),
          }))
        );
      }

      documents = await getSignedURLs(documents);

      return { documents };
    }, "getDocuments");
  }

  /**
   * Get document by ID
   */
  async getDocument(id: string): Promise<any> {
    return this.executeOperation(async () => {
      // Require authentication if enabled
      if (this.authRequired) {
        this.requireAuth();
      }

      const options: DatabaseOptions = {
        where: { id },
        select: {
          id: true,
          name: true,
          path: true,
          file_type: true,
          size: true,
          status: true,
          category: true,
          association_entity: true,
          association_id: true,
          createdAt: true,
          updatedAt: true,
          proposal: true,
          contract: true,
          message: true,
        },
      };

      // Fetch document from database using BaseService method
      const document = await this.findUniqueRecord<Document>(options);

      if (!document) {
        throw new Error("Document not found");
      }

      if (document.path) {
        document.path = await getSignedURL(document.path);
      }

      return document;
    }, "getDocument");
  }

  /**
   * Create a new document
   */
  async createDocument(documentData: CreateDocument): Promise<any> {
    try {
      this.log("info", "Creating new document", { name: documentData.name });

      // Validate input
      const validatedData = CreateDocumentSchema.parse(documentData);

      // Create new document in database using BaseService method
      const newDocument = await this.createRecord<Document>({
        data: {
          ...validatedData,
          createdAt: new Date(),
          updatedAt: new Date(),
        },
      });

      this.log("info", `Document created: ${newDocument.name}`);

      return this.createSuccessResponse(
        newDocument,
        201,
        "Document created successfully"
      );
    } catch (error) {
      this.log("error", `Error creating document: ${error}`);
      return this.createErrorResponse("Failed to create document", 500);
    }
  }

  /**
   * Upload a document
   */
  async uploadDocument(
    file: File,
    location: string,
    metadata: any
  ): Promise<any> {
    try {
      this.log("info", "Uploading document", {
        name: file.name,
        type: file.type,
        size: file.size,
        location,
        metadata,
      });

      // Upload file to S3
      let uploadedPath: any = await UploadToS3(location, file);

      // Create new document in database using BaseService method
      const newDocument = await this.createRecord<Document>({
        data: {
          name: file.name,
          path: uploadedPath,
          file_type: file.type,
          size: file.size.toString(),
          category: metadata.category,
          association_entity: metadata.association_entity,
          association_id: metadata.association_id,
          proposalId: metadata.proposalId,
        },
      });

      this.log("info", `Document created: ${newDocument.name}`);

      return this.createSuccessResponse(
        newDocument,
        201,
        "Document created successfully"
      );
    } catch (error) {
      this.log("error", `Error uploading document: ${error}`);
      return this.createErrorResponse("Failed to upload document", 500);
    }
  }

  /**
   * Update an existing document
   */
  async updateDocument(
    id: string,
    documentData: Partial<UpdateDocument>
  ): Promise<any> {
    try {
      this.log("info", `Updating document: ${id}`, documentData);

      // Update document in database using BaseService method
      const updatedDocument = await this.updateRecord<Document>({
        where: { id },
        data: {
          ...documentData,
          updatedAt: new Date(),
        },
      });

      if (!updatedDocument) {
        this.log("warn", `Document not found for update: ${id}`);
        return this.createErrorResponse("Document not found", 404);
      }

      this.log("info", `Document updated: ${updatedDocument.name}`);

      return this.createSuccessResponse(
        updatedDocument,
        200,
        "Document updated successfully"
      );
    } catch (error) {
      this.log("error", `Error updating document: ${error}`);
      return this.createErrorResponse("Failed to update document", 500);
    }
  }

  /**
   * Delete a document
   */
  async deleteDocument(documentId: string): Promise<any> {
    try {
      this.log("info", `Deleting document: ${documentId}`);

      // Delete document from database using BaseService method
      const deletedDocument = await this.deleteRecord<Document>({
        where: { id: documentId },
      });

      if (!deletedDocument) {
        this.log("warn", `Document not found for deletion: ${documentId}`);
        return this.createErrorResponse("Document not found", 404);
      }

      this.log("info", `Document deleted: ${deletedDocument.name}`);

      return this.createSuccessResponse(
        null,
        200,
        "Document deleted successfully"
      );
    } catch (error) {
      this.log("error", `Error deleting document: ${error}`);
      return this.createErrorResponse("Failed to delete document", 500);
    }
  }

  /**
   * Get document statistics
   */
  async getDocumentStatistics(): Promise<any> {
    try {
      this.log("info", "Fetching document statistics");

      // Get all documents from database
      const documents = await this.findManyRecords<Document>({});

      // Calculate comprehensive statistics from database documents
      const totalSize = documents.reduce((sum: number, doc: Document) => {
        const size = parseInt(doc.size) || 0;
        return sum + size;
      }, 0);

      const statistics = {
        total: documents.length,
        created: documents.filter((d) => d.status === "created").length,
        submitted: documents.filter((d) => d.status === "submitted").length,
        received: documents.filter((d) => d.status === "received").length,
        negotiating: documents.filter((d) => d.status === "negotiating").length,
        agreed: documents.filter((d) => d.status === "agreed").length,
        inprogress: documents.filter((d) => d.status === "inprogress").length,
        reviewing: documents.filter((d) => d.status === "reviewing").length,
        completed: documents.filter((d) => d.status === "completed").length,
        totalSize,
        averageSize:
          documents.length > 0 ? Math.round(totalSize / documents.length) : 0,
        byCategory: documents.reduce((acc: any, doc: Document) => {
          const category = doc.category || "uncategorized";
          acc[category] = (acc[category] || 0) + 1;
          return acc;
        }, {}),
        byFileType: documents.reduce((acc: any, doc: Document) => {
          const fileType = doc.file_type || "unknown";
          acc[fileType] = (acc[fileType] || 0) + 1;
          return acc;
        }, {}),
        byAssociationEntity: documents.reduce((acc: any, doc: Document) => {
          const entity = doc.association_entity || "none";
          acc[entity] = (acc[entity] || 0) + 1;
          return acc;
        }, {}),
      };

      this.log("info", "Document statistics calculated", statistics);

      return this.createSuccessResponse(
        statistics,
        200,
        "Document statistics retrieved successfully"
      );
    } catch (error) {
      this.log("error", `Error fetching document statistics: ${error}`);
      return this.createErrorResponse(
        "Failed to fetch document statistics",
        500
      );
    }
  }
}

import { BaseService, ServiceResponse } from "./base";
import { prisma } from "@/lib/common/prisma";
import { CryptoMiddleware } from "@/lib/crypto/middleware";
/**
 * Authentication service configuration
 */
export interface AuthServiceConfig {
  enableLogging?: boolean;
  throwOnError?: boolean;
}

/**
 * Authentication service for handling login operations
 *
 * This service extends BaseService and provides:
 * 1. Email/password authentication with database validation
 * 2. Password matching against stored user credentials
 * 3. Standardized error handling and logging
 */
export class AuthService extends BaseService {
  constructor(config: AuthServiceConfig = {}) {
    super(config);
    this.setModel(prisma.user);
  }

  /**
   * Authenticate user with email and password
   * @param email - User email address
   * @param password - User password
   * @returns Service response with authentication result
   */
  async login(email: string, password: string): Promise<ServiceResponse> {
    try {
      this.log("info", `Attempting login for email: ${email}`);

      // Find user by email
      const user: any = await this.findUniqueRecord({
        where: { email },
        select: {
          id: true,
          name: true,
          email: true,
          profile: true,
          role: true,
          accounts: { select: { password: true } },
        },
      });

      if (!user) {
        this.log("warn", `User not found for email: ${email}`);
        return this.createErrorResponse(
          `User not found for email: ${email}`,
          401
        );
      }

      const storedPassword = user?.accounts[0]?.password;

      // Check if user has password in accounts
      if (!storedPassword) {
        this.log("warn", `No password found for user: ${email}`);
        return this.createErrorResponse(
          `No password found for user: ${email}`,
          401
        );
      }

      const [hashedPassword, salt] = storedPassword.split("==");

      const valid = await CryptoMiddleware.verifyPassword(
        password,
        hashedPassword,
        salt
      );

      // Match password
      if (!valid) {
        this.log("warn", `Password mismatch for user: ${email}`);
        return this.createErrorResponse(
          `Password mismatch for user: ${email}`,
          401
        );
      }

      // Remove password from response
      delete user.accounts;

      this.log("info", `Login successful for user: ${email}`);

      return this.createSuccessResponse(user, 200, "Login successful");
    } catch (error) {
      this.log("error", `Login error: ${error}`);
      return this.createErrorResponse(
        "An unexpected error occurred during login",
        500
      );
    }
  }

  async register(
    name: string,
    email: string,
    password: string
  ): Promise<ServiceResponse> {
    try {
      this.log("info", `Attempting registration for email: ${email}`);

      if (!name) {
        this.log("warn", `Name is required for registration`);
        return this.createErrorResponse("Name is required", 400);
      }

      if (!email) {
        this.log("warn", `Email is required for registration`);
        return this.createErrorResponse("Email is required", 400);
      }

      if (!password) {
        this.log("warn", `Password is required for registration`);
        return this.createErrorResponse("Password is required", 400);
      }

      // Check if user already exists
      const existingUser = await this.findUniqueRecord({
        where: { email },
      });

      if (existingUser) {
        this.log("warn", `User already exists for email: ${email}`);
        return this.createErrorResponse("User already exists", 409);
      }

      // Password Encryption
      const { hashedPassword, salt } = await CryptoMiddleware.hashPassword(
        password
      );

      // Create user
      const user = await this.createRecord({
        data: {
          name,
          email,
          accounts: {
            create: {
              password: hashedPassword + "==" + salt,
              type: "credentials",
              provider: "credentials",
              providerAccountId: email,
              refresh_token: null,
              access_token: null,
              expires_at: null,
              token_type: null,
              scope: null,
              id_token: null,
              session_state: null,
            },
          },
        },
      });

      this.log("info", `Registration successful for user: ${email}`);

      return this.createSuccessResponse(user, 201, "Registration successful");
    } catch (error) {
      this.log("error", `Registration error: ${error}`);
      return this.createErrorResponse(
        "An unexpected error occurred during registration",
        500
      );
    }
  }
}

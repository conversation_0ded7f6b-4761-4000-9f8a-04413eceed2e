import { z } from "zod";
import { BaseService, DatabaseOptions, ServiceResponse } from "./base";
import { prisma } from "@/lib/common/prisma";
import {
  CreateProposalSchema,
  UpdateProposalSchema,
  ProposalQuerySchema,
} from "@/lib/api/validators/schemas";
import { DatabaseCrypto } from "@/lib/crypto/middleware";

/**
 * Proposal service configuration
 */
export interface ProposalServiceConfig {
  enableLogging?: boolean;
  throwOnError?: boolean;
  validateInput?: boolean;
  validateOutput?: boolean;
  requireAuth?: boolean;
}

/**
 * Proposal service for handling proposal-related operations
 *
 * This service extends BaseService and provides:
 * 1. CRUD operations for proposals
 * 2. Input/output validation using Zod schemas
 * 3. Authentication and authorization checks
 * 4. Standardized error handling and logging
 */
export class ProposalService extends BaseService {
  private authRequired: boolean;

  constructor(config: ProposalServiceConfig = {}) {
    super(config);
    this.authRequired = config.requireAuth ?? true;
    // Set the model to the proposal model delegate
    this.setModel(prisma.proposal);
  }

  /**
   * Create a new proposal
   * @param data - Proposal creation data
   * @returns Service response with created proposal
   */
  async createProposal(data: unknown): Promise<ServiceResponse> {
    return this.executeOperation(async () => {
      // Require authentication if enabled
      if (this.authRequired) {
        super.requireAuth();
      }

      // Validate input data
      const validatedData: any = this.validateInput(CreateProposalSchema, data);

      // Check if user has access to the specified account
      const currentUserId = this.getCurrentUserId();

      if (!currentUserId) {
        throw new Error("User not authenticated");
      }

      // Temporarily set model to account model for this operation
      this.setModel(prisma.account);
      let options: DatabaseOptions = {
        where: {
          // id: validatedData.accountId,
          userId: currentUserId,
        },
      };
      const account: any = await this.findFirstRecord(options);
      // Reset model back to proposal model
      this.setModel(prisma.proposal);

      if (!account) {
        throw new Error("Account not found or access denied");
      }

      // Encrypt sensitive fields before saving
      const proposalData = {
        name: validatedData.name.trim(),
        description: validatedData.description?.trim() || null,
        status: validatedData.status || "created",
        links: validatedData.links || [],
        milestones: validatedData.milestones || [],
        fixed_budget: validatedData.fixed_budget,
        total_budget: validatedData.total_budget,
        duration: validatedData.duration,
        agreed_to_terms_and_conditions:
          validatedData.agreed_to_terms_and_conditions,
        accountId: validatedData.accountId || account?.id,
      };

      console.log("Proposal data:", proposalData);

      // Encrypt sensitive fields (description contains sensitive project details)
      const encryptedData = await DatabaseCrypto.encryptFields(proposalData, [
        "description",
      ]);

      // Prepare create options with contextual structure
      options = {
        data: encryptedData,
        select: {
          name: true,
          description: true,
          status: true,
          links: true,
          milestones: true,
          fixed_budget: true,
          total_budget: true,
          duration: true,
          agreed_to_terms_and_conditions: true,
          attachments: {
            select: {
              id: true,
              name: true,
              file_type: true,
              size: true,
              status: true,
            },
          },
        },
      };

      // Create the proposal using base CRUD operation
      const proposal = await this.createRecord(options);

      return proposal;
    }, "createProposal");
  }

  /**
   * Update an existing proposal
   * @param data - Proposal update data
   * @returns Service response with updated proposal
   */
  async updateProposal(data: unknown): Promise<ServiceResponse> {
    return this.executeOperation(async () => {
      // Require authentication if enabled
      if (this.authRequired) {
        super.requireAuth();
      }

      // Validate input data
      const validatedData: any = this.validateInput(UpdateProposalSchema, data);

      // Build update data (only include provided fields)
      const updateData: any = {};
      if (validatedData.name !== undefined)
        updateData.name = validatedData.name.trim();
      if (validatedData.description !== undefined)
        updateData.description = validatedData.description?.trim() || null;
      if (validatedData.status !== undefined)
        updateData.status = validatedData.status;
      if (validatedData.links !== undefined)
        updateData.links = validatedData.links;
      if (validatedData.milestones !== undefined)
        updateData.milestones = validatedData.milestones;
      if (validatedData.fixed_budget !== undefined)
        updateData.fixed_budget = validatedData.fixed_budget;
      if (validatedData.total_budget !== undefined)
        updateData.total_budget = validatedData.total_budget;
      if (validatedData.duration !== undefined)
        updateData.duration = validatedData.duration;
      if (validatedData.agreed_to_terms_and_conditions !== undefined) {
        updateData.agreed_to_terms_and_conditions =
          validatedData.agreed_to_terms_and_conditions;
      }

      // Build where clause with user access control
      const currentUserId = this.getCurrentUserId();
      const where = {
        id: validatedData.id,
        ...(currentUserId && {
          account: {
            userId: currentUserId,
          },
        }),
      };

      // Prepare update options with contextual structure
      const options: DatabaseOptions = {
        where,
        data: updateData,
        select: {
          account: {
            select: {
              id: true,
              user: {
                select: {
                  id: true,
                  name: true,
                  email: true,
                },
              },
            },
          },
          attachments: {
            select: {
              id: true,
              name: true,
              file_type: true,
              size: true,
              status: true,
            },
          },
        },
      };

      // Update the proposal using base CRUD operation
      // Prisma will automatically throw "Record to update not found" if proposal doesn't exist or user lacks access
      const updatedProposal = await this.updateRecord(options);

      return updatedProposal;
    }, "updateProposal");
  }

  /**
   * Get proposals with filtering and pagination
   * @param query - Query parameters
   * @returns Service response with proposals
   */
  async getProposals(query: unknown): Promise<ServiceResponse> {
    return this.executeOperation(async () => {
      // Require authentication if enabled
      if (this.authRequired) {
        super.requireAuth();
      }

      // Validate query parameters
      const validatedQuery: any = this.validateInput(
        ProposalQuerySchema,
        query || {}
      );

      // Build where clause
      const where: any = {};
      if (validatedQuery.id) where.id = validatedQuery.id;
      if (validatedQuery.status) where.status = validatedQuery.status;
      if (validatedQuery.accountId) where.accountId = validatedQuery.accountId;

      // Add user access filter if authenticated
      const currentUserEmail = this.getCurrentUserEmail();

      if (currentUserEmail) {
        where.account = {
          user: {
            email: currentUserEmail,
          },
        };
      }

      let options: DatabaseOptions = {
        where,
        select: {
          id: true,
          name: true,
          description: true,
          status: true,
          links: true,
          attachments: true,
          milestones: true,
          fixed_budget: true,
          total_budget: true,
          duration: true,
          agreed_to_terms_and_conditions: true,
          createdAt: true,
          updatedAt: true,
          account: {
            select: {
              id: true,
              user: {
                select: {
                  id: true,
                  name: true,
                  email: true,
                },
              },
            },
          },
        },
      };

      // Handle single proposal request
      if (validatedQuery.id) {
        options.where = { id: validatedQuery.id };
        const proposal = await this.findUniqueRecord(options);

        if (!proposal) {
          throw new Error("Proposal not found");
        }

        // Verify user has access to this proposal
        if (
          currentUserEmail &&
          (proposal as any).account.user.email !== currentUserEmail
        ) {
          throw new Error("Access denied");
        }

        return proposal;
      }

      // Prepare query options with contextual structure
      if (validatedQuery.limit) {
        options.take = parseInt(validatedQuery.limit) ?? 0;
      }

      if (validatedQuery.offset) {
        options.skip = parseInt(validatedQuery.offset) ?? 0;
      }

      if (validatedQuery.sortBy) {
        options.orderBy = {
          [validatedQuery.sortBy]: validatedQuery.sortOrder || "desc",
        } as any;
      }

      // Prepare count options with contextual structure
      const countOptions: DatabaseOptions = {
        where,
      };

      // Handle multiple proposals request
      const proposals = await this.findManyRecords(options);
      const total = await this.countRecords(countOptions);

      return {
        proposals,
        pagination: {
          total,
          limit: validatedQuery.limit
            ? parseInt(String(validatedQuery.limit))
            : proposals.length,
          offset: validatedQuery.offset
            ? parseInt(String(validatedQuery.offset))
            : 0,
        },
      };
    }, "getProposals");
  }

  /**
   * Delete a proposal
   * @param proposalId - ID of the proposal to delete
   * @returns Service response
   */
  async deleteProposal(proposalId: string): Promise<ServiceResponse> {
    return this.executeOperation(async () => {
      // Require authentication if enabled
      if (this.authRequired) {
        super.requireAuth();
      }

      // Validate proposal ID
      const validatedId = this.validateInput(z.string().cuid2(), proposalId);

      // Build where clause with user access control
      const currentUserId = this.getCurrentUserId();
      const where = {
        id: validatedId,
        ...(currentUserId && {
          account: {
            userId: currentUserId,
          },
        }),
      };

      // Prepare delete options with contextual structure
      const options: DatabaseOptions = {
        where,
      };

      // Delete the proposal using base CRUD operation
      // Prisma will automatically throw "Record to delete does not exist" if proposal doesn't exist or user lacks access
      await this.deleteRecord(options);

      return { message: "Proposal deleted successfully" };
    }, "deleteProposal");
  }
}

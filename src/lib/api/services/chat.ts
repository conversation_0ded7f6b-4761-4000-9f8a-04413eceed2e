import { BaseService } from "./base";
import {
  RoomSchema,
  MessageSchema,
  CreateRoomSchema,
  CreateMessageSchema,
  UpdateRoomSchema,
  UpdateMessageSchema,
  MemberWithStateSchema,
  type Room,
  type Message,
  type CreateRoom,
  type CreateMessage,
  type UpdateRoom,
  type UpdateMessage,
  type MemberWithState,
  type UserState,
  type TypingEvent,
  type UserStateEvent,
} from "../validators/schemas/chat";
import {
  mockRooms,
  mockMessages,
  mockMembers,
  mockChatUsers,
  mockChatStatistics,
  type ChatStatistics,
} from "@/data/chat-mock";

export class ChatService extends BaseService {
  private rooms: Room[] = [];
  private messages: Message[] = [];
  private members: MemberWithState[] = [];
  private userStates: Map<string, UserState> = new Map();
  private typingUsers: Map<string, Set<string>> = new Map(); // roomId -> Set of userIds

  constructor() {
    super();
    // Initialize with mock data
    this.initializeMockData();
  }

  private initializeMockData() {
    this.rooms = [...mockRooms];
    this.messages = [...mockMessages];
    this.members = [...mockMembers];
    
    // Initialize user states
    this.members.forEach(member => {
      this.userStates.set(member.accountId, member.state);
    });
  }

  private delay(ms: number): Promise<void> {
    return new Promise((resolve) => setTimeout(resolve, ms));
  }

  /**
   * Get all rooms for a user
   */
  async getRooms(userId?: string): Promise<any> {
    try {
      this.log("info", "Fetching chat rooms", { userId });

      await this.delay(300);

      let userRooms = this.rooms;
      
      if (userId) {
        // Filter rooms where user is a member
        const userMemberRooms = this.members
          .filter(member => member.accountId === userId)
          .map(member => member.roomId);
        
        userRooms = this.rooms.filter(room => userMemberRooms.includes(room.id));
      }

      // Enrich rooms with member info and last message
      const enrichedRooms = userRooms.map(room => {
        const roomMembers = this.members.filter(member => member.roomId === room.id);
        const lastMessage = this.messages
          .filter(msg => msg.roomId === room.id)
          .sort((a, b) => new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime())[0];

        return {
          ...room,
          members: roomMembers,
          lastMessage,
          unreadCount: 0, // TODO: Implement unread count logic
        };
      });

      this.log("info", `Found ${enrichedRooms.length} rooms`);

      return this.createSuccessResponse(
        enrichedRooms,
        200,
        "Rooms retrieved successfully"
      );
    } catch (error) {
      this.log("error", `Error fetching rooms: ${error}`);
      return this.createErrorResponse("Failed to fetch rooms", 500);
    }
  }

  /**
   * Get messages for a room
   */
  async getMessages(roomId: string, limit = 50, offset = 0): Promise<any> {
    try {
      this.log("info", `Fetching messages for room: ${roomId}`, { limit, offset });

      await this.delay(200);

      const roomMessages = this.messages
        .filter(msg => msg.roomId === roomId)
        .sort((a, b) => new Date(a.createdAt).getTime() - new Date(b.createdAt).getTime())
        .slice(offset, offset + limit);

      // Enrich messages with sender info
      const enrichedMessages = roomMessages.map(message => {
        const sender = mockChatUsers.find(user => user.id === message.sent_from);
        return {
          ...message,
          sender,
        };
      });

      this.log("info", `Found ${enrichedMessages.length} messages`);

      return this.createSuccessResponse(
        enrichedMessages,
        200,
        "Messages retrieved successfully"
      );
    } catch (error) {
      this.log("error", `Error fetching messages: ${error}`);
      return this.createErrorResponse("Failed to fetch messages", 500);
    }
  }

  /**
   * Send a message
   */
  async sendMessage(messageData: CreateMessage): Promise<any> {
    try {
      this.log("info", "Sending message", { roomId: messageData.roomId });

      // Validate input
      const validatedData = CreateMessageSchema.parse(messageData);

      await this.delay(100);

      // Create new message
      const newMessage: Message = {
        id: `msg-${Date.now()}`,
        ...validatedData,
        createdAt: new Date(),
        updatedAt: new Date(),
      };

      // Add to messages array
      this.messages.push(newMessage);

      // Update room's updatedAt
      const roomIndex = this.rooms.findIndex(room => room.id === messageData.roomId);
      if (roomIndex !== -1) {
        this.rooms[roomIndex].updatedAt = new Date();
      }

      // Enrich message with sender info
      const sender = mockChatUsers.find(user => user.id === newMessage.sent_from);
      const enrichedMessage = {
        ...newMessage,
        sender,
      };

      this.log("info", `Message sent: ${newMessage.id}`);

      return this.createSuccessResponse(
        enrichedMessage,
        201,
        "Message sent successfully"
      );
    } catch (error) {
      this.log("error", `Error sending message: ${error}`);
      return this.createErrorResponse("Failed to send message", 500);
    }
  }

  /**
   * Create a new room
   */
  async createRoom(roomData: CreateRoom): Promise<any> {
    try {
      this.log("info", "Creating new room", { name: roomData.name });

      // Validate input
      const validatedData = CreateRoomSchema.parse(roomData);

      await this.delay(400);

      // Create new room
      const newRoom: Room = {
        id: `room-${Date.now()}`,
        ...validatedData,
        createdAt: new Date(),
        updatedAt: new Date(),
      };

      // Add to rooms array
      this.rooms.push(newRoom);

      this.log("info", `Room created: ${newRoom.name}`);

      return this.createSuccessResponse(
        newRoom,
        201,
        "Room created successfully"
      );
    } catch (error) {
      this.log("error", `Error creating room: ${error}`);
      return this.createErrorResponse("Failed to create room", 500);
    }
  }

  /**
   * Update user state (online/offline/typing/away)
   */
  async updateUserState(userId: string, state: UserState, roomId?: string): Promise<any> {
    try {
      this.log("info", `Updating user state: ${userId} -> ${state}`, { roomId });

      await this.delay(50);

      // Update user state
      this.userStates.set(userId, state);

      // Update member state if in a specific room
      if (roomId) {
        const memberIndex = this.members.findIndex(
          member => member.accountId === userId && member.roomId === roomId
        );
        if (memberIndex !== -1) {
          this.members[memberIndex].state = state;
          this.members[memberIndex].lastSeen = new Date();
        }
      }

      // Handle typing state
      if (state === "typing" && roomId) {
        if (!this.typingUsers.has(roomId)) {
          this.typingUsers.set(roomId, new Set());
        }
        this.typingUsers.get(roomId)!.add(userId);
      } else if (roomId && this.typingUsers.has(roomId)) {
        this.typingUsers.get(roomId)!.delete(userId);
      }

      this.log("info", `User state updated: ${userId}`);

      return this.createSuccessResponse(
        { userId, state, roomId },
        200,
        "User state updated successfully"
      );
    } catch (error) {
      this.log("error", `Error updating user state: ${error}`);
      return this.createErrorResponse("Failed to update user state", 500);
    }
  }

  /**
   * Get typing users for a room
   */
  async getTypingUsers(roomId: string): Promise<any> {
    try {
      const typingUserIds = Array.from(this.typingUsers.get(roomId) || []);
      const typingUsers = mockChatUsers.filter(user => typingUserIds.includes(user.id));

      return this.createSuccessResponse(
        typingUsers,
        200,
        "Typing users retrieved successfully"
      );
    } catch (error) {
      this.log("error", `Error fetching typing users: ${error}`);
      return this.createErrorResponse("Failed to fetch typing users", 500);
    }
  }

  /**
   * Get chat statistics
   */
  async getStatistics(): Promise<any> {
    try {
      this.log("info", "Fetching chat statistics");

      await this.delay(100);

      const stats: ChatStatistics = {
        totalRooms: this.rooms.length,
        totalMessages: this.messages.length,
        activeUsers: this.members.filter(m => m.state !== "offline").length,
        onlineUsers: this.members.filter(m => m.state === "online").length,
      };

      return this.createSuccessResponse(
        stats,
        200,
        "Statistics retrieved successfully"
      );
    } catch (error) {
      this.log("error", `Error fetching statistics: ${error}`);
      return this.createErrorResponse("Failed to fetch statistics", 500);
    }
  }
}

# API Validators

This directory contains Zod schemas and validation services for API endpoints based on the Prisma schema models.

## Structure

```
validators/
├── services/
│   └── data.ts          # DataValidator OOP service
├── schemas/
│   ├── common.ts        # Common schemas and enums
│   ├── document.ts      # Document model schemas
│   ├── proposal.ts      # Proposal model schemas
│   └── index.ts         # Schema exports
├── index.ts             # Main exports and convenience validators
└── README.md           # This file
```

## Usage

### Basic Schema Validation

```typescript
import { CreateProposalSchema, DocumentQuerySchema } from '@/lib/api/validators';

// Validate proposal creation data
const result = CreateProposalSchema.safeParse(requestData);
if (result.success) {
  // Data is valid
  const validData = result.data;
} else {
  // Handle validation errors
  console.log(result.error.issues);
}
```

### Using DataValidator Service

```typescript
import { DataValidator, CreateProposalSchema } from '@/lib/api/validators';

const validator = DataValidator.createStrict();

// Boolean validation
const isValid = validator.isValid(CreateProposalSchema, requestData);

// Detailed validation
const result = validator.validate(CreateProposalSchema, requestData);
if (result.isValid) {
  // Use result.data
} else {
  // Handle result.errors
}
```

### Using Convenience Validators

```typescript
import { ApiValidators, validateCreateProposal } from '@/lib/api/validators';

// Using the class
const result = ApiValidators.validateCreateProposal(requestData);

// Using the function
const result2 = validateCreateProposal(requestData);

// Quick boolean check
const isValid = ApiValidators.isValidCreateProposal(requestData);
```

## Available Schemas

### Proposal Schemas
- `BaseProposalSchema` - Core proposal fields without metadata
- `ProposalSchema` - Complete proposal with metadata
- `CreateProposalSchema` - For API creation requests
- `UpdateProposalSchema` - For API update requests
- `ProposalQuerySchema` - For API query parameters
- `ProposalBusinessValidationSchema` - With business logic validation

### Document Schemas
- `BaseDocumentSchema` - Core document fields without metadata
- `DocumentSchema` - Complete document with metadata
- `CreateDocumentSchema` - For API creation requests
- `UpdateDocumentSchema` - For API update requests
- `DocumentQuerySchema` - For API query parameters

### Common Schemas
- `StatusSchema` - Status enum validation
- `IdSchema` - CUID validation
- `DateTimeSchema` - Date validation
- `RequiredStringSchema` - Non-empty string validation

## Integration with API Routes

```typescript
// In your API route
import { validateCreateProposal } from '@/lib/api/validators';

export async function POST(request: NextRequest) {
  const body = await request.json();
  
  const validation = validateCreateProposal(body);
  if (!validation.isValid) {
    return NextResponse.json(
      { error: "Validation failed", details: validation.errors },
      { status: 400 }
    );
  }
  
  // Use validation.data for the validated and transformed data
  const proposal = await prisma.proposal.create({
    data: validation.data
  });
  
  return NextResponse.json({ success: true, data: proposal });
}
```

import { z } from "zod";

/**
 * Status enum schema based on Prisma Status enum
 */
export const StatusSchema = z.enum([
  "submitted",
  "received", 
  "negotiating",
  "agreed",
  "created",
  "inprogress",
  "reviewing",
  "completed"
]);

/**
 * Common field schemas
 */
export const IdSchema = z.string().cuid();
export const DateTimeSchema = z.date();
export const OptionalStringSchema = z.string().optional();
export const RequiredStringSchema = z.string().min(1, "This field is required");

/**
 * Validation helpers
 */
export const createOptionalField = <T>(schema: z.ZodType<T>) => schema.optional();
export const createRequiredField = <T>(schema: z.ZodType<T>) => schema;

/**
 * Type exports for use in other files
 */
export type Status = z.infer<typeof StatusSchema>;

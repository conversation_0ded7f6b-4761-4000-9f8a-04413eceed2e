import { z } from "zod";
import { IdSchema, RequiredStringSchema } from "./common";

/**
 * User state enum for chat
 */
export const UserStateSchema = z.enum([
  "online",
  "offline",
  "typing",
  "away"
]);

/**
 * Base Room schema based on Prisma Room model
 * Excludes metadata fields: id, createdAt, updatedAt
 */
export const BaseRoomSchema = z.object({
  name: RequiredStringSchema,
  about: z.string().optional(),
  contractId: z.string().cuid().optional(),
});

/**
 * Complete Room schema including metadata fields
 */
export const RoomSchema = BaseRoomSchema.extend({
  id: IdSchema,
  createdAt: z.date(),
  updatedAt: z.date(),
});

/**
 * Room creation schema (for API requests)
 */
export const CreateRoomSchema = z.object({
  name: RequiredStringSchema,
  about: z.string().optional(),
  contractId: z.string().cuid().optional(),
});

/**
 * Room update schema (for API requests)
 */
export const UpdateRoomSchema = z.object({
  id: IdSchema,
  name: RequiredStringSchema.optional(),
  about: z.string().optional(),
  contractId: z.string().cuid().optional(),
});

/**
 * Base Member schema based on Prisma Member model
 */
export const BaseMemberSchema = z.object({
  accountId: z.string().cuid(),
  roomId: z.string().cuid(),
});

/**
 * Complete Member schema including metadata fields
 */
export const MemberSchema = BaseMemberSchema.extend({
  id: IdSchema,
});

/**
 * Member with user state for real-time features
 */
export const MemberWithStateSchema = MemberSchema.extend({
  state: UserStateSchema.default("offline"),
  lastSeen: z.date().optional(),
});

/**
 * Base Message schema based on Prisma Message model
 * Excludes metadata fields: id, createdAt, updatedAt
 */
export const BaseMessageSchema = z.object({
  content: RequiredStringSchema,
  sent_from: z.string().cuid(),
  sent_to: z.string().cuid(),
  roomId: z.string().cuid().optional(),
  associations: z.array(z.any()).default([]),
});

/**
 * Complete Message schema including metadata fields
 */
export const MessageSchema = BaseMessageSchema.extend({
  id: IdSchema,
  createdAt: z.date(),
  updatedAt: z.date(),
});

/**
 * Message creation schema (for API requests)
 */
export const CreateMessageSchema = z.object({
  content: RequiredStringSchema,
  sent_from: z.string().cuid(),
  sent_to: z.string().cuid(),
  roomId: z.string().cuid().optional(),
  associations: z.array(z.any()).default([]).optional(),
});

/**
 * Message update schema (for API requests)
 */
export const UpdateMessageSchema = z.object({
  id: IdSchema,
  content: RequiredStringSchema.optional(),
});

/**
 * Chat room query schema (for API requests)
 */
export const RoomQuerySchema = z.object({
  id: IdSchema.optional(),
  contractId: z.string().cuid().optional(),
  limit: z.coerce.number().int().positive().max(100).optional(),
  offset: z.coerce.number().int().min(0).optional(),
});

/**
 * Message query schema (for API requests)
 */
export const MessageQuerySchema = z.object({
  roomId: z.string().cuid().optional(),
  sent_from: z.string().cuid().optional(),
  sent_to: z.string().cuid().optional(),
  limit: z.coerce.number().int().positive().max(100).optional(),
  offset: z.coerce.number().int().min(0).optional(),
});

/**
 * Real-time event schemas
 */
export const TypingEventSchema = z.object({
  roomId: z.string().cuid(),
  userId: z.string().cuid(),
  isTyping: z.boolean(),
});

export const UserStateEventSchema = z.object({
  userId: z.string().cuid(),
  state: UserStateSchema,
  roomId: z.string().cuid().optional(),
});

/**
 * Type exports
 */
export type Room = z.infer<typeof RoomSchema>;
export type BaseRoom = z.infer<typeof BaseRoomSchema>;
export type CreateRoom = z.infer<typeof CreateRoomSchema>;
export type UpdateRoom = z.infer<typeof UpdateRoomSchema>;
export type RoomQuery = z.infer<typeof RoomQuerySchema>;

export type Member = z.infer<typeof MemberSchema>;
export type BaseMember = z.infer<typeof BaseMemberSchema>;
export type MemberWithState = z.infer<typeof MemberWithStateSchema>;

export type Message = z.infer<typeof MessageSchema>;
export type BaseMessage = z.infer<typeof BaseMessageSchema>;
export type CreateMessage = z.infer<typeof CreateMessageSchema>;
export type UpdateMessage = z.infer<typeof UpdateMessageSchema>;
export type MessageQuery = z.infer<typeof MessageQuerySchema>;

export type UserState = z.infer<typeof UserStateSchema>;
export type TypingEvent = z.infer<typeof TypingEventSchema>;
export type UserStateEvent = z.infer<typeof UserStateEventSchema>;

"use client";

import { toast } from "sonner";

interface NotificationOptions {
  title: string;
  body: string;
  icon?: string;
  tag?: string;
  data?: any;
  onClick?: () => void;
}

class ChatNotificationService {
  private permission: NotificationPermission = "default";
  private isSupported: boolean = false;

  constructor() {
    if (typeof window !== "undefined") {
      this.isSupported = "Notification" in window;
      this.permission = this.isSupported ? Notification.permission : "denied";
    }
  }

  /**
   * Request notification permission from the user
   */
  async requestPermission(): Promise<NotificationPermission> {
    if (!this.isSupported) {
      console.warn("Notifications are not supported in this browser");
      return "denied";
    }

    if (this.permission === "granted") {
      return "granted";
    }

    try {
      this.permission = await Notification.requestPermission();
      return this.permission;
    } catch (error) {
      console.error("Error requesting notification permission:", error);
      return "denied";
    }
  }

  /**
   * Show a browser notification
   */
  private showBrowserNotification(options: NotificationOptions): Notification | null {
    if (!this.isSupported || this.permission !== "granted") {
      return null;
    }

    try {
      const notification = new Notification(options.title, {
        body: options.body,
        icon: options.icon || "/favicon.ico",
        tag: options.tag,
        data: options.data,
        requireInteraction: false,
        silent: false,
      });

      if (options.onClick) {
        notification.onclick = options.onClick;
      }

      // Auto-close after 5 seconds
      setTimeout(() => {
        notification.close();
      }, 5000);

      return notification;
    } catch (error) {
      console.error("Error showing notification:", error);
      return null;
    }
  }

  /**
   * Show a toast notification (fallback or additional)
   */
  private showToastNotification(options: NotificationOptions): void {
    toast.info(options.title, {
      description: options.body,
      action: options.onClick ? {
        label: "View",
        onClick: options.onClick,
      } : undefined,
    });
  }

  /**
   * Show notification for new message
   */
  showMessageNotification(
    senderName: string,
    message: string,
    roomName: string,
    onClickCallback?: () => void
  ): void {
    const options: NotificationOptions = {
      title: `${senderName} in ${roomName}`,
      body: message.replace(/<[^>]*>/g, '').trim().slice(0, 100), // Strip HTML and limit length
      tag: `chat-message-${roomName}`,
      data: {
        type: "chat-message",
        roomName,
        senderName,
      },
      onClick: onClickCallback,
    };

    // Show browser notification if permission granted
    const browserNotification = this.showBrowserNotification(options);
    
    // Always show toast as fallback or additional notification
    if (!browserNotification) {
      this.showToastNotification(options);
    }
  }

  /**
   * Show notification for user joining/leaving
   */
  showUserStatusNotification(
    userName: string,
    status: "joined" | "left",
    roomName: string
  ): void {
    const options: NotificationOptions = {
      title: roomName,
      body: `${userName} ${status} the conversation`,
      tag: `user-status-${roomName}`,
      data: {
        type: "user-status",
        roomName,
        userName,
        status,
      },
    };

    // Only show toast for user status (less intrusive)
    this.showToastNotification(options);
  }

  /**
   * Show notification for typing indicators
   */
  showTypingNotification(userName: string, roomName: string): void {
    // Only show as toast, and only briefly
    toast.info(`${userName} is typing...`, {
      duration: 2000,
      id: `typing-${userName}-${roomName}`, // Prevent duplicates
    });
  }

  /**
   * Check if notifications are supported and enabled
   */
  isEnabled(): boolean {
    return this.isSupported && this.permission === "granted";
  }

  /**
   * Get current permission status
   */
  getPermission(): NotificationPermission {
    return this.permission;
  }

  /**
   * Check if notifications are supported
   */
  isNotificationSupported(): boolean {
    return this.isSupported;
  }
}

// Create singleton instance
export const chatNotifications = new ChatNotificationService();

// Hook for React components
export function useChatNotifications() {
  const requestPermission = async () => {
    return await chatNotifications.requestPermission();
  };

  const showMessageNotification = (
    senderName: string,
    message: string,
    roomName: string,
    onClickCallback?: () => void
  ) => {
    chatNotifications.showMessageNotification(senderName, message, roomName, onClickCallback);
  };

  const showUserStatusNotification = (
    userName: string,
    status: "joined" | "left",
    roomName: string
  ) => {
    chatNotifications.showUserStatusNotification(userName, status, roomName);
  };

  const showTypingNotification = (userName: string, roomName: string) => {
    chatNotifications.showTypingNotification(userName, roomName);
  };

  return {
    requestPermission,
    showMessageNotification,
    showUserStatusNotification,
    showTypingNotification,
    isEnabled: chatNotifications.isEnabled(),
    isSupported: chatNotifications.isNotificationSupported(),
    permission: chatNotifications.getPermission(),
  };
}

// Auto-request permission on first load (optional)
export function initializeChatNotifications() {
  if (typeof window !== "undefined") {
    // Request permission after a short delay to avoid blocking initial page load
    setTimeout(() => {
      if (chatNotifications.getPermission() === "default") {
        chatNotifications.requestPermission();
      }
    }, 2000);
  }
}

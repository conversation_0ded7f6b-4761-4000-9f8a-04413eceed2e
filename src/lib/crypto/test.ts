/**
 * Test file for the crypto library (crypto-js version)
 * Run with: node -r ts-node/register src/lib/crypto/test.ts
 */

import { crypto, password, dataEncryption, session } from "./utils";
import { CryptoLibrary } from "./encryption";
import { initializeCrypto, validation } from "./config";
import { CryptoMiddleware, DatabaseCrypto } from "./middleware";

async function runTests() {
  console.log("🔐 Testing Crypto Library\n");

  // Initialize and validate configuration
  const initResult = initializeCrypto();
  console.log("📋 Configuration:", initResult);
  console.log("");

  // Test 1: Basic encryption/decryption
  console.log("🧪 Test 1: Basic Encryption/Decryption");
  try {
    const originalData = "This is sensitive information";
    const encrypted = await crypto.encrypt(originalData);
    const decrypted = await crypto.decrypt(encrypted);

    console.log("✅ Original:", originalData);
    console.log("✅ Encrypted:", encrypted);
    console.log("✅ Decrypted:", decrypted);
    console.log("✅ Match:", originalData === decrypted);
  } catch (error) {
    console.error("❌ Basic encryption test failed:", error);
  }
  console.log("");

  // Test 2: Password hashing
  console.log("🧪 Test 2: Password Hashing");
  try {
    const plainPassword = "mySecurePassword123!";
    const { hash, salt } = await password.hash(plainPassword);
    const isValid = await password.verify(plainPassword, hash, salt);
    const isInvalid = await password.verify("wrongPassword", hash, salt);

    console.log("✅ Password hash:", hash);
    console.log("✅ Salt:", salt);
    console.log("✅ Valid password verification:", isValid);
    console.log("✅ Invalid password verification:", isInvalid);
  } catch (error) {
    console.error("❌ Password hashing test failed:", error);
  }
  console.log("");

  // Test 3: Data encryption for objects
  console.log("🧪 Test 3: Object Data Encryption");
  try {
    const userData = {
      email: "<EMAIL>",
      phone: "+1234567890",
      ssn: "***********",
    };

    const encrypted = await dataEncryption.encryptUserData(userData);
    const decrypted = await dataEncryption.decryptUserData(encrypted);

    console.log("✅ Original object:", userData);
    console.log("✅ Encrypted:", encrypted);
    console.log("✅ Decrypted object:", decrypted);
    console.log(
      "✅ Objects match:",
      JSON.stringify(userData) === JSON.stringify(decrypted)
    );
  } catch (error) {
    console.error("❌ Object encryption test failed:", error);
  }
  console.log("");

  // Test 4: Session token management
  console.log("🧪 Test 4: Session Token Management");
  try {
    const sessionData = {
      userId: "user-123",
      email: "<EMAIL>",
      role: "admin",
    };

    const token = await session.createToken(sessionData);
    const verified = await session.verifyToken(token);

    console.log("✅ Session token created:", token);
    console.log("✅ Session verified:", verified);
    console.log("✅ User ID match:", verified?.userId === sessionData.userId);
  } catch (error) {
    console.error("❌ Session token test failed:", error);
  }
  console.log("");

  // Test 5: Database field encryption
  console.log("🧪 Test 5: Database Field Encryption");
  try {
    const proposalData = {
      id: "proposal-123",
      name: "Public Project Name",
      description: "This is sensitive project description",
      clientEmail: "<EMAIL>",
      budget: 50000,
    };

    const sensitiveFields = ["description", "clientEmail"];
    const encrypted = await DatabaseCrypto.encryptFields(
      proposalData,
      sensitiveFields
    );
    const decrypted = await DatabaseCrypto.decryptFields(
      encrypted,
      sensitiveFields
    );

    console.log("✅ Original proposal:", proposalData);
    console.log("✅ Encrypted proposal:", encrypted);
    console.log("✅ Decrypted proposal:", decrypted);
    console.log(
      "✅ Sensitive fields encrypted:",
      encrypted.description !== proposalData.description &&
        encrypted.clientEmail !== proposalData.clientEmail
    );
    console.log(
      "✅ Decryption successful:",
      JSON.stringify(proposalData) === JSON.stringify(decrypted)
    );
  } catch (error) {
    console.error("❌ Database field encryption test failed:", error);
  }
  console.log("");

  // Test 6: Custom crypto instance
  console.log("🧪 Test 6: Custom Crypto Instance");
  try {
    const customCrypto = new CryptoLibrary({
      algorithm: "aes256",
      encoding: "hex",
      key: "my-custom-key-32-characters-long",
    });

    const data = "Custom encryption test";
    const result = await customCrypto.encrypt(data);
    const decrypted = await customCrypto.decrypt(result.encrypted, result.key);

    console.log("✅ Custom encryption result:", result);
    console.log("✅ Custom decryption:", decrypted);
    console.log("✅ Custom encryption match:", data === decrypted);
  } catch (error) {
    console.error("❌ Custom crypto test failed:", error);
  }
  console.log("");

  // Test 7: HMAC and integrity verification
  console.log("🧪 Test 7: HMAC and Integrity Verification");
  try {
    const data = "Important data that needs integrity verification";
    const hmacValue = await crypto.hmac(data);
    const isValid = await crypto.verifyHMAC(data, hmacValue);
    const isInvalid = await crypto.verifyHMAC("tampered data", hmacValue);

    console.log("✅ Original data:", data);
    console.log("✅ HMAC value:", hmacValue);
    console.log("✅ Valid HMAC verification:", isValid);
    console.log("✅ Invalid HMAC verification:", isInvalid);
  } catch (error) {
    console.error("❌ HMAC test failed:", error);
  }
  console.log("");

  // Test 8: Validation utilities
  console.log("🧪 Test 8: Validation Utilities");
  try {
    const testHash = await crypto.hash("test data");
    const testEncrypted = await crypto.encrypt("test data");

    console.log("✅ Hash validation:", validation.isValidHash(testHash));
    console.log(
      "✅ Encrypted string validation:",
      validation.isValidEncryptedString(testEncrypted)
    );
    console.log(
      "✅ Secure key validation:",
      validation.isSecureKey("this-is-a-secure-key-with-enough-length")
    );
    console.log(
      "✅ Insecure key validation:",
      validation.isSecureKey("password")
    );
  } catch (error) {
    console.error("❌ Validation test failed:", error);
  }
  console.log("");

  console.log("🎉 All tests completed!");
}

// Export test function for external use
export { runTests };

// Run tests if this file is executed directly
if (require.main === module) {
  runTests().catch(console.error);
}

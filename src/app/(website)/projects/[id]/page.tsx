"use client";

import useS<PERSON> from "swr";

import fetcher from "@/lib/common/requests";

import parse from "html-react-parser";
import { trimming, removeSpaces } from "@/lib/common/utils";

import { motion } from "framer-motion";
import Link from "next/link";
import { useRouter } from "next/navigation";
import { useParams } from "next/navigation";
import { usePathname, useSearchParams } from "next/navigation";

import { EmailSubscriber } from "@/modules";
import { Thumbnailed as ProjectCard, CtaBtns } from "@/components/common";

import { Swiper, SwiperSlide, useSwiper, useSwiperSlide } from "swiper/react";

// Icons
import {
  HiMiniArrowLongLeft as LeftArrow,
  HiMiniArrowLongRight as RightArrow,
} from "react-icons/hi2";
import { BsShare as ShareIcon } from "react-icons/bs";
import { GoDotFill as PageIcon } from "react-icons/go";

// Import Swiper styles
import "swiper/css";
import "swiper/css/navigation";

// import required modules
import { Autoplay } from "swiper/modules";
import { useEffect, useState } from "react";
import React from "react";

interface BannerProps {
  image: any;
  title: string;
  caption: string;
  client: string;
  date: string;
  website: string;
}

interface MetaProps {
  label: string;
  value: string;
}

const Project = () => {
  return (
    <>
      <section className="w-full flex flex-col gap-36 items-stretch justify-between text-white bg-gradient-to-b from-white to-lime-50">
        {/* Recent works */}
        <Layout />
      </section>
      <section className="w-full bg-lime-500">
        <RelatedProjects />
      </section>
    </>
  );
};

const Layout = () => {
  const pathname = usePathname();
  const searchParams = useSearchParams();
  const stateOn = searchParams.get("on");
  let query = pathname.split("/");

  const { data, isLoading, error } = useSWR(
    `projects/${stateOn ?? query[query.length - 1]}`,
    fetcher
  );

  const banner = {
    image: data?.image,
    title: data?.title,
    caption: data?.caption,
    client: data?.client,
    date: data?.date?.split("T")[0],
    website: data?.links?.length > 0 ? data.links[0].url : "",
  };

  const content = {
    type: data?.type,
    description: data?.description,
    categories: data?.categories,
  };

  const gallery = {
    pictures: data?.gallery,
  };

  return (
    <span className="w-full h-full flex flex-col pt-1 md:pt-6">
      <Banner {...banner} />
      <Content {...content} />
      <Gallery {...gallery} />
    </span>
  );
};

const Banner = ({
  image,
  title,
  caption,
  client,
  date,
  website,
}: BannerProps) => {
  const Meta = ({ label, value }: MetaProps) => {
    return (
      <span className="flex flex-col gap-1">
        <p className="text-navy-100">{label}</p>
        <p className="text-white">{value}</p>
      </span>
    );
  };

  return (
    <span className="w-full h-[50vh] relative overflow-hidden">
      <motion.span
        initial={{ opacity: 0 }}
        animate={{ opacity: 1 }}
        transition={{ duration: 0.8, ease: "backIn" }}
        className="w-full h-full absolute inset-0"
        style={{
          backgroundImage: `url(${window.location.origin + image?.url})`,
        }}
      />
      <motion.span
        initial={{ opacity: 0 }}
        animate={{ opacity: 1 }}
        transition={{ duration: 0.8, ease: "easeIn" }}
        className="w-full h-full absolute inset-0 bg-black/[.4] flex flex-col justify-end p-8"
      >
        <span className="w-full flex flex-col gap-8">
          <span className="w-full flex flex-col gap-4">
            <h2 className="text-white">{title}</h2>
            <p className="text-lime-250">{caption}</p>
          </span>
          <span className="w-full flex flex-row gap-8">
            <Meta label="Client" value={client} />
            <Meta label="Date" value={date} />
            <Meta label="Website" value={website} />
          </span>
        </span>
      </motion.span>
    </span>
  );
};

const Content = ({
  type,
  description,
  categories,
}: {
  type?: string;
  description?: string;
  categories?: any[];
}) => {
  const [copy, setCopy] = useState(false);
  const pathname = usePathname();

  useEffect(() => setCopy(false), [pathname]);

  // Sub-components
  const Wishlist = () => {
    const searchParams = useSearchParams();
    const stateOn = searchParams.get("on");
    return (
      <span className="flex flex-col gap-2 text-black">
        <h6 className="capitalize">stay in the know</h6>
        <p>Keep up-to-date with the latest developments on this project.</p>
        <EmailSubscriber
          data={{ interest: ["wishlist"], product: [stateOn] }}
          props={{}}
        />
      </span>
    );
  };

  const Touchbase = () => {
    return (
      <span className="flex flex-col gap-6 text-black">
        <span className="flex flex-col gap-2">
          <p className="font-bold">Interested in something similar?</p>
          <p>
            Connect with us, We're eager to engage in a fruitful discussion
            about your ideas.
          </p>
        </span>
        <CtaBtns theme={"light"} />
      </span>
    );
  };

  const CTA: Record<string, React.ReactElement> = {
    saas: <Wishlist />,
    project: <Touchbase />,
  };

  const Categories = () => {
    return (
      <span className="w-full flex flex-col gap-4">
        <h6 className="font-bold capitalize text-black">categories</h6>
        <span className="w-max flex flex-col items-start gap-1">
          {categories?.map((category: any, index: number) => {
            return (
              <p key={index} className="capitalize text-lime-400">
                {category?.name}
              </p>
            );
          })}
        </span>
      </span>
    );
  };

  return (
    <section className="w-full flex flex-col xl:flex-row pt-12 gap-12 xl:gap-0 xl:gap-28 justify-between items-start">
      <p className="text-black text-wrap max-w-4xl">
        {parse(`${description}`)}
      </p>
      <span className="w-full h-full flex flex-col gap-8 items-start justify-center md:flex-row md:items-start md:justify-between md:gap-0 md:gap-16 xl:flex-col xl:items-start xl:justify-center xl:gap-0 xl:gap-8">
        {type && CTA[type]}
        <Categories />
        <button
          className="w-max outline black"
          onClick={() => {
            navigator.clipboard.writeText(window.location.href);
            setCopy(true);
          }}
        >
          {copy ? (
            "Share link copied ✅"
          ) : (
            <span className="flex flex-row items-center justify-center gap-2">
              <ShareIcon size={14} />
              <p>Share</p>
            </span>
          )}
        </button>
      </span>
    </section>
  );
};

const Gallery = ({ pictures }: { pictures?: any[] }) => {
  const transitionDelay = 8000;

  // Sub-components
  const Controls = () => {
    const swiper = useSwiper();
    const [pages, setPages] = useState<React.ReactElement[] | null>(null);
    const [activePage, setActivePage] = useState(0);

    function shuffle() {
      let { slides, activeIndex } = swiper ?? {};

      setPages(
        slides
          ?.map((_: any, index: number) => (
            <PageIcon
              key={index}
              size={activeIndex == index ? 18 : 15}
              onClick={() => {
                setActivePage(index);
                swiper.slideTo(index);
              }}
              className={`page ${activeIndex == index ? "active" : "inactive"}`}
            />
          ))
          .slice(0, slides?.length - 2) ?? null
      );
    }

    useEffect(() => {
      shuffle();
    }, [activePage]);

    return (
      <span className="w-full h-full flex flex-row items-center justify-between">
        <LeftArrow
          size={25}
          color="black"
          className="hover:scale-125 duration-300"
          onClick={() => {
            swiper.slidePrev();
            shuffle();
          }}
        />
        <span className="pagination">{pages}</span>
        <RightArrow
          size={25}
          color="black"
          className="hover:scale-125 duration-300"
          onClick={() => {
            swiper.slideNext();
            shuffle();
          }}
        />
      </span>
    );
  };

  // * Main component
  return (
    <span className="gap-8">
      <h4 className="text-black">Gallery</h4>
      <Swiper
        spaceBetween={30}
        centeredSlides={false}
        oneWayMovement={true}
        autoplay={{
          delay: transitionDelay,
          disableOnInteraction: true,
        }}
        breakpoints={{
          320: {
            slidesPerView: 1,
            spaceBetween: 10,
          },
          480: {
            slidesPerView: 1,
            spaceBetween: 20,
          },
          640: {
            slidesPerView: 2,
            spaceBetween: 30,
          },
          1080: {
            slidesPerView: 3,
            spaceBetween: 30,
          },
        }}
        slidesPerView={3}
        modules={[Autoplay]}
        className="gap-6"
      >
        {pictures?.map((img: any, index: number) => {
          return (
            <SwiperSlide key={index}>
              <img src={window.location.origin + img?.picture?.url} alt="" />
            </SwiperSlide>
          );
        })}
        <Controls />
      </Swiper>
    </span>
  );
};

const RelatedProjects = () => {
  return (
    <div className="w-full flex flex-col gap-12">
      <h4 className="capitalize">related projects</h4>
      <Projects />
    </div>
  );
};

const Projects = () => {
  const pathname = usePathname();
  const searchParams = useSearchParams();
  const stateOn = searchParams.get("on");
  let query = pathname.split("/");

  const { data, isLoading, error } = useSWR(
    `projects?where[id][not_equals]=${
      stateOn ?? query[query?.length - 1]
    }&depth=1&drafts=false&limit=2`,
    fetcher
  );

  return (
    <span className="w-full grid grid-cols-1 md:grid-cols-2 xl:grid-flow-row-dense gap-12 xl:gap-12">
      {data?.map((project: any, index: number) => {
        let { id, title } = project ?? {};
        return (
          <ProjectCard
            key={index}
            {...project}
            navigateTo={{
              internal: true,
              url: `/projects/${removeSpaces(title)}/${id}`,
              params: { state: { on: id } },
            }}
          />
        );
      })}
    </span>
  );
};

export default Project;

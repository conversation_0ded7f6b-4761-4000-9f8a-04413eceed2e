"use client";

import useS<PERSON> from "swr";
import fetcher from "@/lib/common/requests";
import { removeSpaces } from "@/lib/common/utils";
import { Thumbnailed as Card, GridArt } from "@/components/common";

const Projects = () => {
  return (
    <>
      <GridArt amount={6} />
      <section className="w-full flex flex-col gap-8 md:gap-12 xl:gap-28 items-stretch justify-between text-white">
        {/* Recent works */}
        <h3 className="capitalize pt-4 md:pt-8">projects</h3>
        <ProjectsData />
      </section>
    </>
  );
};

const ProjectsData = () => {
  const { data, isLoading, error } = useSWR(
    "projects?depth=1&drafts=false",
    fetcher
  );

  return (
    <span className="w-full grid grid-cols-1 md:grid-cols-2 md:grid-flow-row-dense gap-6 xl:gap-16">
      {data?.map((project: unknown, index: number) => {
        const { title, id } =
          (project as { title?: string; id?: string }) ?? {};
        return (
          <Card
            key={index}
            {...(project as object)}
            navigateTo={{
              internal: true,
              url: `${removeSpaces(title)}/${id}`,
              params: { state: { on: id } },
            }}
          />
        );
      })}
    </span>
  );
};

export default Projects;

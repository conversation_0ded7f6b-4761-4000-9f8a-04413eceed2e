"use client";

import { Heading, Thumbnailed as Card, GridArt } from "@/components/common";

// Request
import useSWR from "swr";
import fetcher from "@/lib/common/requests";
import { removeSpaces } from "@/lib/common/utils";

const Products = () => {
  return (
    <main>
      <GridArt amount={6} />
      <section className="w-full h-full flex flex-col gap-12 justify-end items-center">
        <Listing />
      </section>
    </main>
  );
};

const Listing = () => {
  const url = "products?depth=1&drafts=false";

  const { data } = useSWR(url, fetcher);

  return (
    <div className="w-full gap-16">
      <Heading
        tagline={"marketplace"}
        title={"Design, Code, Automate - All in One Marketplace"}
      />
      <span
        className={
          "h-[80vh] overflow-y grid grid-cols-1 md:grid-cols-2 xl:grid-cols-3 gap-5 md:gap-6 lg:gap-8 xl:gap-10"
        }
      >
        {data?.map((product: unknown, index: number) => {
          const { id, title, price } =
            (product as { id?: string; title?: string; price?: number }) ?? {};
          return (
            <Card
              key={index}
              {...(product as object)}
              caption={`Price: $${price}`}
              navigateTo={{
                internal: true,
                url: `market/${id}`,
                params: { state: { on: id } },
              }}
            />
          );
        })}
      </span>
    </div>
  );
};

export default Products;

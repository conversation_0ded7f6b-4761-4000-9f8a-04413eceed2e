export const coreServices = {
  width: "w-full xl:w-1/2",
  data: [
    {
      title: "Enterprise Systems (ERP & BES)",
      caption:
        "Unify your operations and unlock hidden potential with Underscor's next-generation ERP & BES solutions. Experience lightning speed, gain deep data insights, and enjoy complete security and data ownership. Our fully customizable systems scale flawlessly, while our 24/7 support keeps your business running smoothly. Don't settle for average, empower your success with Underscor.",
      icon: "erp",
    },
    {
      title: "Consultancy",
      caption:
        "Need guidance and expertise? Our consultants offer a range of services to help you  at any stage of the software development lifecycle, from strategic planning to implementation and ongoing support",
      icon: "consultancy",
    },
  ],
};

export const additionalServices = {
  width: "w-full xl:w-1/2",
  data: [
    {
      title: "UI/UX - Research & Design",
      caption:
        "We don't just build software, we craft experiences. Our research and design team gets to the heart of your users' needs, then designs interfaces that are intuitive, delightful, and drive results",
      icon: "ui_ux",
    },
    {
      title: "Application development",
      caption:
        "Our experienced engineers & developers will translate designs into robust, scalable applications using the latest technologies.",
      icon: "application",
    },
    {
      title: "System Architecture",
      caption:
        "Our system architects create solid technical backbones that ensures your software is secure, reliable, and future-proof.",
      icon: "system",
    },
  ],
  theme: "gray",
};

export const ourprocess = [
  {
    title: "Onboarding",
    caption:
      "We start by getting to know you and your project inside-out. This collaborative process ensures we understand your goals, target audience, and desired functionalities.",
    style: {
      width: "w-full md:w-2/6",
      theme: "outline",
    },
  },
  {
    title: "Design & Development",
    caption:
      "Our design team translates your vision into a user-centric experience. We'll craft intuitive interfaces and a visually stunning aesthetic that resonates with your users..",
    style: {
      width: "w-full md:w-1/2",
      theme: "outline",
    },
    position: "center",
  },
  {
    title: "QA & Preview",
    caption:
      "Our skilled developers bring your design to life. We utilize cutting-edge technology to build a robust and secure platform that meets all your needs. This is our favorite part.",
    style: {
      width: "w-full md:w-1/2",
      theme: "outline",
    },
    position: "end",
  },
  {
    title: "Launch",
    caption:
      "We don't just build it, we launch it with success. We'll meticulously plan and execute your launch, ensuring a smooth transition for your users and maximum impact.",
    style: {
      width: "w-full md:w-1/4",
      theme: "outline",
    },
    position: "end",
  },
];

export const corevalues = [
  {
    title: "Innovation",
    caption:
      "We push boundaries.  We're passionate about crafting cutting-edge solutions that solve real-world problems and redefine what's possible.",
  },
  {
    title: "Collaboration",
    caption:
      "We believe the best ideas come together.  We foster a collaborative environment where diverse perspectives combine to create exceptional outcomes.",
    style: "gray",
  },
  {
    title: "Excellence",
    caption:
      "We settle for nothing less than the best.  We meticulously craft every detail of your project, ensuring the highest quality and exceeding your expectations.",
    style: "black",
  },
];

export const testimonials = [
  [
    {
      company: "atlassian",
      message: "Had a very great experience, working with this company...",
      from: {
        name: "Robert Micardo",
        designation: "CTO, Head of Tech",
      },
    },
    {
      company: "dropbox",
      message: "Had a very great experience, working with this company...",
      from: {
        name: "Miles Mason",
        designation: "CTO, Head of Tech",
      },
    },
  ],
  [
    {
      company: "microsoft",
      message: "Had a very great experience, working with this company...",
      from: {
        name: "Lilian Marcus",
        designation: "CTO, Head of Tech",
      },
    },
    {
      company: "salesforce",
      message: "Had a very great experience, working with this company...",
      from: {
        name: "Fred Muju",
        designation: "CTO, Head of Tech",
      },
    },
    {
      company: "sap",
      message: "Had a very great experience, working with this company...",
      from: {
        name: "Amina Dylan",
        designation: "CTO, Head of Tech",
      },
    },
  ],
  [
    {
      company: "slack",
      message: "Had a very great experience, working with this company...",
      from: {
        name: "Peter Steward",
        designation: "CTO, Head of Tech",
      },
    },
    {
      company: "ibm",
      message: "Had a very great experience, working with this company...",
      from: {
        name: "Vivian Klass",
        designation: "CTO, Head of Tech",
      },
    },
  ],
];

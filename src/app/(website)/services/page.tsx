"use client";

import {
  corevalues,
  ourprocess,
  testimonials,
  coreServices,
  additionalServices,
} from "./data";

import { motion } from "framer-motion";

import { CtaBtns } from "@/components/common";
import Pill from "@/components/common/ui/pill";
import { TypeAnimation } from "react-type-animation";

import { GiBullseye as AccuracyIcon } from "react-icons/gi";
import { SiOctopusdeploy as OctopusIcon } from "react-icons/si";
import { TbAutomaticGearbox as AutonomousIcon } from "react-icons/tb";

import DataWaveTheme from "@/modules/themes/datawave";
import { GridArt } from "@/components/common";
import { ListLayout, RowTabs, StaggeredLayout, Testimonials } from "./modules";

const Services = () => {
  return (
    <main className="w-full h-full gap-4">
      <GridArt />
      <DataWaveTheme>
        <Hero />
      </DataWaveTheme>
      <CoreServices />
      <AdditionalServices />
      <Process />
      <CoreValues />
      {/* Until later */}
      {/* <Testimonial /> */}
    </main>
  );
};

const Hero = () => {
  const transition = { ease: "easeInOut", duration: 200 };

  return (
    <motion.section
      initial={false}
      animate={{ opacity: 100 }}
      transition={transition}
      exit={{ opacity: 0 }}
      className="h-screen -bottom-10 flex flex-col gap-12 xl:gap-0 xl:flex-row xl:justify-between xl:items-end"
    >
      <div className="w-full flex flex-col gap-8 items-start justify-center">
        <div className="flex flex-row gap-3">
          <Pill>
            <AutonomousIcon size={12} color={"#fbfbfb"} />
            <p>Autonomous</p>
          </Pill>
          <Pill>
            <AccuracyIcon size={14} color={"#fbfbfb"} /> <p>Accurate</p>
          </Pill>
          <Pill>
            <OctopusIcon size={12} color={"#fbfbfb"} />
            <p>Multi-purpose</p>
          </Pill>
        </div>
        <CtaBtns />
      </div>
      {/* Hero */}
      <span className="w-max flex flex-col gap-8 items-start justify-center text-end">
        <motion.h1
          initial={false}
          animate={{ opacity: 100, transition }}
          transition={transition}
          className="w-max h-max inline-block bg-clip-text text-transparent bg-linear-to-r from-zinc-600 to-zinc-500 mix-blend-darker leading-tight"
        >
          Today's Enterprise is
          <br />{" "}
          <TypeAnimation
            sequence={["Fast", 4000, "Agentic", 4000, "& Scalable", 4000]}
            wrapper="span"
            // style={{ color: "#25F4A1" }}
            speed={6}
            repeat={Infinity}
          />{" "}
          Solutions
        </motion.h1>
      </span>
    </motion.section>
  );
};

const CoreServices = () => {
  return (
    <ListLayout
      heading={{
        sub: "We offer:",
        main: "Enterprise - AI First Architecture",
      }}
      services={coreServices}
      captions={
        [
          "Committed to excellence:",
          "ISO 27001:2022 A 8.25 and ISO/IEC/IEEE12207:2017 for SDLC",
        ] as string[]
      }
    />
  );
};

const AdditionalServices = () => {
  return (
    <ListLayout
      heading={{ main: "More - services" }}
      services={additionalServices}
    />
  );
};

const Process = () => {
  return (
    <section className="w-full flex flex-col gap-12 justify-start items-start">
      <span className="flex flex-col -gap-3">
        <h3 className="text-lime-300 capitalize">from vision to reality:</h3>
        <h3 className="capitalize">our process</h3>
      </span>
      <StaggeredLayout data={ourprocess} />
    </section>
  );
};

const CoreValues = () => {
  return (
    <section className="w-full flex flex-col justify-start items-start gap-16">
      <span className="flex flex-col -gap-3">
        <h3 className="text-lime-300 capitalize">
          our core values:{" "}
          <span className="text-white">
            building <br /> excellence together
          </span>
        </h3>
      </span>
      <RowTabs list={corevalues} />
    </section>
  );
};

const Testimonial = () => {
  return (
    <section className="gap-16">
      <h3 className="capitalize">testimonials </h3>
      <Testimonials data={testimonials} />
    </section>
  );
};

export default Services;

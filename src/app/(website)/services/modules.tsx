"use client";

import { motion } from "framer-motion";
import { ListTab, RowTab, TestimonyCard } from "@/components/common";

interface ListLayoutProps {
  heading: {
    sub?: string;
    main: string;
  };
  services: unknown[];
  captions?: string[];
}

interface StaggeredLayoutProps {
  data?: unknown[];
}

interface ListTabsProps {
  width?: string;
  data?: unknown[];
  theme?: string;
}

interface RowTabsProps {
  list?: unknown[];
}

interface TestimonialsProps {
  data?: unknown[][];
}

export const ListLayout = ({
  heading,
  services,
  captions = [],
}: ListLayoutProps) => {
  return (
    <section className="w-full h-max flex flex-col gap-8 xl:gap-0 xl:flex-row justify-between items-start">
      <span className="flex flex-col">
        <span className="mb-8 md:mb-12 xl:mb-44 flex flex-col lg:gap-1 xl:-gap-5">
          <h5>{heading?.sub}</h5>
          <h3 className="text-lime-300 capitalize">{heading?.main}</h3>
        </span>
        {captions?.map((caption: string, index: number) => {
          return (
            <p key={index} className="text-navy-100">
              {caption}
            </p>
          );
        })}
      </span>
      <ListTabs {...services} />
    </section>
  );
};

// Motion Settings
const animations = {
  initial: { scaleX: 0, opacity: 0 },
  whileInView: { scaleX: 1, opacity: 100 },
  viewport: { once: true },
  transition: {
    duration: 1,
    ease: "backInOut",
  },
};

export const StaggeredLayout = ({ data = [] }: StaggeredLayoutProps) => {
  return (
    <span className="w-full flex flex-col -gap-[1px]">
      {data?.map((value: unknown, index: number) => {
        return (
          <motion.span key={index} {...animations} className="origin-left">
            <ListTab {...(value as object)} />
          </motion.span>
        );
      })}
    </span>
  );
};

export const ListTabs = ({
  width = "w-full",
  data = [],
  theme = "lime",
}: ListTabsProps) => {
  return (
    <span className={`${width} flex flex-col gap-[1px]`}>
      {data?.map((value: unknown, index: number) => {
        return (
          <motion.span key={index} {...animations}>
            <ListTab
              {...(value as object)}
              style={{ width: "w-full", theme }}
            />
          </motion.span>
        );
      })}
    </span>
  );
};

export const RowTabs = ({ list = [] }: RowTabsProps) => {
  return (
    <span className={`w-full flex flex-col gap-[1px]`}>
      {list?.map((value: unknown, index: number) => {
        return (
          <motion.span key={index} {...animations} className="origin-top">
            <RowTab {...(value as object)} />
          </motion.span>
        );
      })}
    </span>
  );
};

export const Testimonials = ({ data = [] }: TestimonialsProps) => {
  return (
    <span className="w-full flex flex-col gap-4">
      {data?.map((column: unknown[], i: number) => {
        return (
          <span
            key={i}
            className="w-full flex flex-col gap-4 md:gap-0 md:flex-row md:gap-4"
          >
            {column?.map((row: unknown, j: number) => {
              return <TestimonyCard key={j} {...(row as object)} />;
            })}
          </span>
        );
      })}
    </span>
  );
};

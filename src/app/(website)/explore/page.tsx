"use client";

import { useEffect, useState } from "react";

import useS<PERSON> from "swr";
import fetcher from "@/lib/common/requests";

import { Heading, News as Card, Carousel } from "@/components/common";

const API_KEY = "f71f1099e905749db4c7c4238c014611";
const NORMAL_NEWS_BASE_URL = `https://gnews.io/api/v4?apikey=${API_KEY}&lang=en&country=us`;
const BREAKING_NEWS_BASE_URL = `https://gnews.io/api/v4/top-headlines?apikey=${API_KEY}&lang=en&country=us`;

const Explore = () => {
  // This due to budget constraints where we cannot accommodate this API cost
  const [ifNewsIsAvailable, setIfNewsIsAvailable] = useState(false);
  const url = `${BREAKING_NEWS_BASE_URL}&q=breaking%20news&category=business&max=1`;

  const { data } = useSWR(url, fetcher);

  useEffect(() => {
    const { articles } = data ?? {};
    setIfNewsIsAvailable(articles?.length > 0);
  }, [data]);

  return (
    <section className="w-full h-full flex flex-col gap-12 justify-end items-center">
      {ifNewsIsAvailable && (
        <div className="w-full grid-cols-1 md:grid-cols-2 gap-12">
          <TopHeadLine />
          <LatestNews />
        </div>
      )}
      <div className="w-full flex flex-col gap-12">
        {ifNewsIsAvailable && <Articles />}
        <OurFeed />
        {ifNewsIsAvailable && <MoreNews />}
      </div>
    </section>
  );
};

const TopHeadLine = () => {
  const url = `${BREAKING_NEWS_BASE_URL}&q=breaking%20news&category=technology&max=1`;
  const { data } = useSWR(url, fetcher);

  if (!data?.articles?.length)
    return (
      <div>
        <p>No posts yet</p>
      </div>
    );

  const article = data?.articles?.[0] ?? {};

  return (
    <Container className="w-full h-full gap-12">
      <Heading title={"Headline news today"} />
      <Card type="large" {...article} />
    </Container>
  );
};

const LatestNews = () => {
  const url = `${BREAKING_NEWS_BASE_URL}&q=ai%20machine%20learning&category=business`;
  const { data } = useSWR(url, fetcher);

  if (!data?.articles?.length) return <></>;

  return (
    <Container className="w-full flex flex-col gap-12">
      <Heading title={"Latest news"} />
      <span className="w-full h-[50em] flex flex-col gap-8 overflow-y-scroll">
        {data?.articles?.map((article: any, index: number) => {
          return <Card key={index} type="small" {...article} />;
        })}
      </span>
    </Container>
  );
};

const Articles = () => {
  const url = `${NORMAL_NEWS_BASE_URL}&q=ai%20artificial%20intelligence&category=space`;
  const { data } = useSWR(url, fetcher);

  return (
    <Container className="w-full flex flex-col gap-8">
      <Heading title="Articles" />
      <div className="w-full max-w-[100vw] flex flex-row overflow-x-scroll gap-16">
        {data?.articles?.map((article: unknown, index: number) => {
          return <Card key={index} {...(article as object)} type="medium" />;
        })}
      </div>
    </Container>
  );
};

const OurFeed = () => {
  const { data } = useSWR(
    "https://feeds.behold.so/sklzOCEvPCxz4MgdX0An",
    fetcher
  );

  const pictures = data?.posts?.map(
    (img: { sizes?: { large?: { mediaUrl?: string } } }) =>
      img?.sizes?.large?.mediaUrl
  );

  if (!data?.posts?.length) return <></>;

  return (
    <Container>
      <Heading title="Our Feed" />
      <Carousel pictures={pictures} />
    </Container>
  );
};

const MoreNews = () => {
  const url = `${NORMAL_NEWS_BASE_URL}&q=ai%20machine%20learning&category=technology`;
  const { data } = useSWR(url, fetcher);

  return (
    <Container>
      <Heading title="More News" />
      <div className="w-full grid grid-cols-1 lg:grid-cols-2 gap-12">
        {data?.articles?.map((article: unknown, index: number) => {
          return <Card key={index} {...(article as object)} type="medium" />;
        })}
      </div>
    </Container>
  );
};

interface ContainerProps {
  children: React.ReactNode;
  className?: string;
}

const Container = ({
  children,
  className = "w-full flex flex-col gap-8",
}: ContainerProps) => <div className={className}>{children}</div>;

export default Explore;

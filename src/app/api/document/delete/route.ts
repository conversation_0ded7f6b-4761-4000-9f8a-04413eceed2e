"use server";

import { NextRequest, NextResponse } from "next/server";
import { auth } from "@/lib/auth";
import { DocumentService } from "@/lib/api/services/document";

export async function DELETE(request: NextRequest) {
  try {
    // Initialize document service
    const session = await auth();
    const documentService = new DocumentService();

    // Set the service context with session and request
    documentService.setContext({
      session,
      user: session?.user,
      request,
    });

    // Extract request body
    const body = await request.json();
    const { id } = body;

    if (!id) {
      return NextResponse.json(
        { success: false, error: "Document ID is required" },
        { status: 400 }
      );
    }

    // Use the service to delete document
    const result = await documentService.deleteDocument(id);

    return NextResponse.json(result, {
      status: result.success ? 200 : result.statusCode || 500,
    });
  } catch (error) {
    console.error("Document delete error:", error);
    return NextResponse.json(
      { success: false, error: "Internal server error" },
      { status: 500 }
    );
  }
}

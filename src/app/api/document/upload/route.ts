"use server";

import { NextRequest, NextResponse } from "next/server";
import { auth } from "@/lib/auth";
import { DocumentService } from "@/lib/api/services/document";

export async function POST(request: NextRequest) {
  try {
    // Initialize document service
    const session = await auth();
    const documentService = new DocumentService();

    // Set the service context with session and request
    documentService.setContext({
      session: session || undefined,
      user: session?.user,
      request,
    });

    // Extract form data
    const formData = await request.formData();
    const file = formData.get("file") as File;
    const location = formData.get("location") as string;

    // Extract metadata from form data
    const metadata = {
      status: formData.get("status") as string,
      category: formData.get("category") as string,
      association_entity: formData.get("association_entity") as string,
      association_id: formData.get("association_id") as string,
      proposalId: formData.get("proposalId") as string,
    };

    // Use the service to upload document
    const result = await documentService.uploadDocument(
      file,
      location,
      metadata
    );

    return NextResponse.json(result, { status: 201 });
  } catch (error) {
    console.error("Document upload error:", error);
    return NextResponse.json(
      { error: "Internal server error" },
      { status: 500 }
    );
  }
}

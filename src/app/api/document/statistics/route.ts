"use server";

import { auth } from "@/lib/auth";
import { NextRequest, NextResponse } from "next/server";
import { DocumentService } from "@/lib/api/services/document";

export async function GET(request: NextRequest) {
  try {
    // Initialize document service
    const session = await auth();
    const documentService = new DocumentService();

    // Set the service context with session and request
    documentService.setContext({
      session: session || undefined,
      user: session?.user,
      request,
    });

    // Get document statistics directly from the service
    const result = await documentService.getDocumentStatistics();

    if (result.success) {
      return NextResponse.json(result, { status: 200 });
    } else {
      throw new Error(result.message || "Failed to get document statistics");
    }
  } catch (error) {
    console.error("Document statistics error:", error);
    return NextResponse.json(
      { success: false, error: "Internal server error" },
      { status: 500 }
    );
  }
}

"use server";

import { NextRequest, NextResponse } from "next/server";
import { auth } from "@/lib/auth";
import { DocumentService } from "@/lib/api/services/document";

export async function GET(request: NextRequest) {
  try {
    // Initialize document service
    const session = await auth();
    const documentService = new DocumentService();

    // Set the service context with session and request
    documentService.setContext({
      session: session || undefined,
      user: session?.user,
      request,
    });

    // Extract query parameters from URL
    const { searchParams } = new URL(request.url);
    const queryParams = Object.fromEntries(searchParams.entries());

    // Check if requesting a specific document by ID
    if (queryParams.id) {
      const result = await documentService.getDocument(queryParams.id);
      return NextResponse.json(result, { status: result.success ? 200 : 404 });
    }

    // Use the service to get all documents
    const result = await documentService.getDocuments();

    return NextResponse.json(result, { status: 200 });
  } catch (error) {
    console.error("Documents GET error:", error);
    return NextResponse.json(
      { error: "Internal server error" },
      { status: 500 }
    );
  }
}

"use server";

import { NextRequest, NextResponse } from "next/server";
import { auth } from "@/lib/auth";
import { ProposalService } from "@/lib/api/services/proposal";

export async function DELETE(request: NextRequest) {
  try {
    // Initialize proposal service
    const session = await auth();
    const proposalService = new ProposalService({
      requireAuth: true,
    });

    // Set the service context with session and request
    proposalService.setContext({
      session,
      user: session?.user,
      request,
    });

    // Extract request body
    const body = await request.json();
    const { id } = body;

    if (!id) {
      return NextResponse.json(
        { error: "Proposal ID is required" },
        { status: 400 }
      );
    }

    // Use the service to delete proposal
    const result = await proposalService.deleteProposal(id);

    return NextResponse.json(result, { status: 200 });
  } catch (error) {
    console.error("Proposal DELETE error:", error);
    return NextResponse.json(
      { error: "Internal server error" },
      { status: 500 }
    );
  }
}

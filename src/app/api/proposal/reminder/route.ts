"use server";

import { NextRequest, NextResponse } from "next/server";
import { auth } from "@/lib/auth";
import { z } from "zod";
import { prisma } from "@/lib/common/prisma";

// Validation schema for reminder request
const ReminderRequestSchema = z.object({
  proposalId: z.string().cuid(),
  message: z.string().min(1, "Message is required"),
  scheduledDate: z.string().datetime(),
});

export async function POST(request: NextRequest) {
  try {
    // Check authentication
    const session = await auth();
    if (!session?.user) {
      return NextResponse.json(
        {
          success: false,
          error: true,
          message: "Authentication required",
          statusCode: 401,
          timestamp: new Date().toISOString(),
        },
        { status: 401 }
      );
    }

    // Parse and validate request body
    const body = await request.json();
    const validatedData = ReminderRequestSchema.parse(body);

    // Check if proposal exists and user has access
    const proposal = await prisma.proposal.findFirst({
      where: {
        id: validatedData.proposalId,
        account: {
          userId: session.user.id,
        },
      },
      include: {
        account: true,
      },
    });

    if (!proposal) {
      return NextResponse.json(
        {
          success: false,
          error: true,
          message: "Proposal not found or access denied",
          statusCode: 404,
          timestamp: new Date().toISOString(),
        },
        { status: 404 }
      );
    }

    // Check if proposal is eligible for reminders (older than 1 week)
    const oneWeekAgo = new Date();
    oneWeekAgo.setDate(oneWeekAgo.getDate() - 7);

    if (new Date(proposal.createdAt) > oneWeekAgo) {
      return NextResponse.json(
        {
          success: false,
          error: true,
          message: "Reminders can only be sent for proposals older than 1 week",
          statusCode: 400,
          timestamp: new Date().toISOString(),
        },
        { status: 400 }
      );
    }

    // Check if proposal status allows reminders
    if (proposal.status === "completed" || proposal.status === "agreed") {
      return NextResponse.json(
        {
          success: false,
          error: true,
          message: "Reminders cannot be sent for completed or agreed proposals",
          statusCode: 400,
          timestamp: new Date().toISOString(),
        },
        { status: 400 }
      );
    }

    // TODO: In a real implementation, you would:
    // 1. Check reminder count from database (create a reminders table)
    // 2. Ensure less than 3 reminders have been sent
    // 3. Store the reminder in database
    // 4. Schedule the reminder (using a job queue like Bull/Agenda)
    // 5. Send the actual reminder via email/notification service

    // For now, we'll simulate the reminder creation
    const reminderData = {
      proposalId: validatedData.proposalId,
      message: validatedData.message,
      scheduledDate: new Date(validatedData.scheduledDate),
      status: "scheduled",
      createdAt: new Date(),
    };

    // In a real app, you'd save this to a reminders table:
    // const reminder = await prisma.reminder.create({
    //   data: reminderData
    // });

    console.log("Reminder scheduled:", reminderData);

    return NextResponse.json({
      success: true,
      error: false,
      data: {
        id: `reminder_${Date.now()}`, // Mock ID
        ...reminderData,
      },
      message: "Reminder scheduled successfully",
      statusCode: 200,
      timestamp: new Date().toISOString(),
    });
  } catch (error) {
    console.error("Reminder API error:", error);

    if (error instanceof z.ZodError) {
      return NextResponse.json(
        {
          success: false,
          error: true,
          message: "Invalid request data",
          errors: error.errors,
          statusCode: 400,
          timestamp: new Date().toISOString(),
        },
        { status: 400 }
      );
    }

    return NextResponse.json(
      {
        success: false,
        error: true,
        message: "Internal server error",
        statusCode: 500,
        timestamp: new Date().toISOString(),
      },
      { status: 500 }
    );
  }
}

"use server";

import { auth } from "@/lib/auth";
import { NextRequest, NextResponse } from "next/server";
import { ProposalService } from "@/lib/api/services/proposal";

export async function GET(request: NextRequest) {
  try {
    // Initialize proposal service
    const session = await auth();
    const proposalService = new ProposalService({
      requireAuth: true,
    });

    // Set the service context with session and request
    proposalService.setContext({
      session: session || undefined,
      user: session?.user,
      request,
    });

    // Extract query parameters from URL
    const { searchParams } = new URL(request.url);
    const queryParams = Object.fromEntries(searchParams.entries());

    // Use the service to get proposals
    const result = await proposalService.getProposals(queryParams);

    return NextResponse.json(result, { status: 200 });
  } catch (error) {
    console.error("Proposal GET error:", error);
    return NextResponse.json(
      { error: "Internal server error" },
      { status: 500 }
    );
  }
}

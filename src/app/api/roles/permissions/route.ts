import { NextRequest, NextResponse } from "next/server";
import { auth } from "@/lib/auth";
import { RoleService } from "@/lib/api/services/role";

/**
 * PUT /api/roles/permissions
 * Update role permissions
 */
export async function PUT(request: NextRequest) {
  try {
    // Get session for authentication
    const session = await auth();

    // Initialize role service
    const roleService = new RoleService({
      requireAuth: true,
      enableLogging: true,
      validateInput: true,
      validateOutput: false,
    });

    // Set the service context with session and request
    roleService.setContext({
      session: session || undefined,
      user: session?.user,
      request,
    });

    // Parse request body
    const body = await request.json();

    // Use the service to update role permissions
    const result = await roleService.updateRolePermissions(body);

    // Return standardized response format
    return NextResponse.json(result, { status: 200 });
  } catch (error) {
    console.error("Role permissions update error:", error);
    return NextResponse.json(error, { status: 500 });
  }
}

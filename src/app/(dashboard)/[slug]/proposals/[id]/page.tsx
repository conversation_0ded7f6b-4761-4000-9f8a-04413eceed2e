"use client";

import React, { Suspense, use } from "react";
import { ProposalDetailsContainer } from "@/components/view/proposals/details/container";

interface ProposalDetailsPageProps {
  id: string;
  slug: string;
}

export default function ProposalDetailsPage({
  params,
}: {
  params: Promise<{ id: string; slug: string }>;
}) {
  const { id, slug } = use(params);
  const proposal: ProposalDetailsPageProps = {
    id,
    slug,
  };
  return (
    <Suspense fallback={<div>Loading proposal details...</div>}>
      <ProposalDetailsContainer proposalId={proposal?.id} />
    </Suspense>
  );
}

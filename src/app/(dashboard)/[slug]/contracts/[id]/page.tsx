"use client";

import React, { Suspense } from "react";
import { ContractDetailsContainer } from "@/components/view/contracts/details/container";

interface ContractDetailsPageProps {
  id: string;
  slug: string;
}

export default function ContractDetailsPage({
  params,
}: {
  params: { id: string; slug: string };
}) {
  const contract: ContractDetailsPageProps = {
    id: params.id,
    slug: params.slug,
  };
  return (
    <Suspense fallback={<div>Loading contract details...</div>}>
      <ContractDetailsContainer contractId={contract?.id} />
    </Suspense>
  );
}

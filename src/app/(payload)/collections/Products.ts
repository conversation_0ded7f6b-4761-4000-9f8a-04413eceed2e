import type { CollectionConfig } from "payload";

const Products: CollectionConfig = {
  slug: "products",
  access: {
    read: () => true,
  },
  admin: {
    useAsTitle: "title",
  },
  fields: [
    {
      name: "title",
      label: "Title",
      type: "text",
      required: true,
    },
    {
      name: "caption",
      label: "Caption",
      type: "text",
      defaultValue: "",
    },
    {
      name: "date",
      label: "Date",
      type: "date",
      defaultValue: "2024/01/01",
    },
    {
      name: "details",
      label: "Details",
      type: "array",
      fields: [
        {
          name: "title",
          type: "text",
        },
        {
          name: "description",
          type: "text",
        },
      ],
      required: true,
    },
    {
      name: "features",
      label: "Features",
      type: "array",
      fields: [
        {
          name: "name",
          type: "text",
        },
      ],
    },
    {
      name: "categories",
      label: "Categories",
      type: "relationship",
      relationTo: "categories",
      hasMany: true,
      admin: {
        allowCreate: true,
      },
    },
    {
      name: "price",
      label: "Price",
      type: "number",
      defaultValue: 0,
      required: true,
    },
    {
      name: "image",
      label: "Image",
      type: "upload",
      relationTo: "media",
    },
    {
      name: "gallery",
      label: "Gallery",
      type: "array",
      fields: [
        {
          name: "picture",
          label: "picture",
          type: "upload",
          relationTo: "media",
        },
      ],
    },
  ],
};

export default Products;

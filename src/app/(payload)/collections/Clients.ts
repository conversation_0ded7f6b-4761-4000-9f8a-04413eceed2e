import type { CollectionConfig } from "payload";

const Clients: CollectionConfig = {
  slug: "clients",
  access: {
    read: () => true,
    create: () => true,
    update: () => true,
  },
  admin: {
    useAsTitle: "email",
  },
  fields: [
    {
      name: "email",
      label: "Client",
      type: "email",
      unique: true,
      required: true,
      defaultValue: "blank",
    },
    {
      name: "interest",
      label: "Interest",
      type: "select",
      options: ["wishlist", "newsletter"],
      hasMany: true,
    },
    {
      name: "project",
      label: "Project",
      type: "relationship",
      hasMany: true,
      relationTo: "projects",
    },
  ],
};

export default Clients;

import type { CollectionConfig } from "payload";

const Projects: CollectionConfig = {
  slug: "projects",
  access: {
    read: () => true,
  },
  admin: {
    useAsTitle: "title",
  },
  fields: [
    {
      name: "title",
      label: "Title",
      type: "text",
      required: true,
    },
    {
      name: "caption",
      label: "Caption",
      type: "text",
    },
    {
      name: "client",
      label: "Client",
      type: "text",
    },
    {
      name: "type",
      label: "Type",
      type: "select",
      options: [
        {
          label: "project",
          value: "project",
        },
        {
          label: "saas",
          value: "saas",
        },
      ],
    },
    {
      name: "date",
      label: "Date",
      type: "date",
      defaultValue: "2024/01/01",
    },
    {
      name: "description",
      label: "Description",
      type: "textarea",
      required: true,
    },
    {
      name: "categories",
      label: "Categories",
      type: "relationship",
      relationTo: "categories",
      hasMany: true,
      admin: {
        allowCreate: true,
      },
    },
    {
      name: "image",
      label: "Image",
      type: "upload",
      relationTo: "media",
    },
    {
      name: "gallery",
      label: "Gallery",
      type: "array",
      fields: [
        {
          name: "picture",
          label: "picture",
          type: "upload",
          relationTo: "media",
        },
      ],
    },
    {
      name: "links",
      label: "Links",
      type: "array",
      maxRows: 4,
      fields: [
        {
          name: "label",
          type: "text",
        },
        {
          name: "url",
          type: "text",
        },
      ],
    },
  ],
};

export default Projects;

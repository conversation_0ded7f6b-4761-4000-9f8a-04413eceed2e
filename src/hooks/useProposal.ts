"use client";

import useS<PERSON> from "swr";
import { api } from "@/lib/common/requests";
import { useCallback } from "react";
import { useSelector, useDispatch } from "react-redux";
import { RootState, AppDispatch } from "@/store";
import {
  selectCurrentProposal,
  selectIsCreating,
  selectIsUpdating,
  selectIsDeleting,
  selectError,
  clearError,
  setCreating,
  setUpdating,
  setDeleting,
  setError,
  setCurrentProposal,
} from "@/store/slices/proposal";
import type {
  CreateProposal,
  UpdateProposal,
  Proposal,
} from "@/lib/api/validators/schemas/proposal";
import type { ProposalStats } from "@/data/proposals-mock";

// Fetcher function for SWR using api module
const fetcher = async (url: string) => {
  try {
    // Remove '/api/' prefix since api module already includes it
    const endpoint = url.startsWith("/api/") ? url.slice(5) : url;
    return await api.get(endpoint);
  } catch (error) {
    console.error("Fetcher error:", error);
    throw error;
  }
};

export function useProposal() {
  const dispatch = useDispatch<AppDispatch>();

  // Redux selectors for operation states
  const currentProposal = useSelector((state: RootState) =>
    selectCurrentProposal(state)
  );
  const isCreating = useSelector((state: RootState) => selectIsCreating(state));
  const isUpdating = useSelector((state: RootState) => selectIsUpdating(state));
  const isDeleting = useSelector((state: RootState) => selectIsDeleting(state));
  const error = useSelector((state: RootState) => selectError(state));

  // SWR hooks for data fetching
  const {
    data: proposalsData,
    error: proposalsError,
    isLoading: isLoadingProposals,
    mutate: mutateProposals,
  } = useSWR("/api/proposal/get", fetcher);

  const {
    data: statisticsData,
    error: statisticsError,
    isLoading: isLoadingStats,
    mutate: mutateStatistics,
  } = useSWR("/api/proposal/statistics", fetcher);

  // Extract data from SWR responses
  const proposals: Proposal[] = proposalsData?.data.proposals || [];
  const statistics: ProposalStats | null = statisticsData?.data || null;
  const isLoading = isLoadingProposals;

  // Handle SWR errors
  const swrError = proposalsError || statisticsError;
  if (swrError && !error) {
    dispatch(setError(swrError.message));
  }

  // API operations
  const create = useCallback(
    async (proposalData: CreateProposal) => {
      try {
        dispatch(setCreating(true));
        dispatch(clearError());

        const result = await api.post("proposal/create", proposalData);

        // Refresh data after creation
        await mutateProposals();
        await mutateStatistics();

        return result;
      } catch (error) {
        const errorMessage =
          error instanceof Error ? error.message : "Failed to create proposal";
        dispatch(setError(errorMessage));
        throw error;
      } finally {
        dispatch(setCreating(false));
      }
    },
    [dispatch, mutateProposals, mutateStatistics]
  );

  const update = useCallback(
    async (proposalData: UpdateProposal) => {
      try {
        dispatch(setUpdating(true));
        dispatch(clearError());

        const result = await api.put("proposal/update", proposalData);

        // Update current proposal if it's the one being updated
        if (currentProposal?.id === proposalData.id) {
          dispatch(setCurrentProposal(result));
        }

        // Refresh data after update
        await mutateProposals();
        await mutateStatistics();

        return result;
      } catch (error) {
        const errorMessage =
          error instanceof Error ? error.message : "Failed to update proposal";
        dispatch(setError(errorMessage));
        throw error;
      } finally {
        dispatch(setUpdating(false));
      }
    },
    [dispatch, currentProposal, mutateProposals, mutateStatistics]
  );

  const remove = useCallback(
    async (proposalId: string) => {
      try {
        dispatch(setDeleting(true));
        dispatch(clearError());

        await api.delete("proposal/delete", { id: proposalId });

        // Clear current proposal if it's the one being deleted
        if (currentProposal?.id === proposalId) {
          dispatch(setCurrentProposal(null));
        }

        // Refresh data after deletion
        await mutateProposals();
        await mutateStatistics();

        return { success: true };
      } catch (error) {
        const errorMessage =
          error instanceof Error ? error.message : "Failed to delete proposal";
        dispatch(setError(errorMessage));
        throw error;
      } finally {
        dispatch(setDeleting(false));
      }
    },
    [dispatch, currentProposal, mutateProposals, mutateStatistics]
  );

  // Utility functions
  const fetchById = useCallback(
    async (proposalId: string) => {
      try {
        const result = await api.get(`proposal/get?id=${proposalId}`);
        if (result.success && !result.error) {
          dispatch(setCurrentProposal(result.data || result));
          return result.data || result;
        } else {
          throw new Error(result.message || "Failed to fetch proposal");
        }
      } catch (error) {
        const errorMessage =
          error instanceof Error ? error.message : "Failed to fetch proposal";
        dispatch(setError(errorMessage));
        throw error;
      }
    },
    [dispatch]
  );

  const clearErrorState = useCallback(() => {
    dispatch(clearError());
  }, [dispatch]);

  return {
    // State
    proposals,
    currentProposal,
    statistics,
    isLoading,
    isCreating,
    isUpdating,
    isDeleting,
    isLoadingStats,
    error,

    // Actions
    create,
    fetchById,
    update,
    remove,
    clearError: clearErrorState,

    // SWR utilities
    mutateProposals,
    mutateStatistics,

    // Computed values
    hasProposals: proposals.length > 0,
    isAnyLoading:
      isLoading || isCreating || isUpdating || isDeleting || isLoadingStats,
  };
}

"use client";

import use<PERSON><PERSON> from "swr";
import { api } from "@/lib/common/requests";
import { useCallback } from "react";
import { useSelector, useDispatch } from "react-redux";
import type { RootState, AppDispatch } from "@/store";
import {
  clearError,
  setCurrentDocument,
  clearCurrentDocument,
  setCreating,
  setUpdating,
  setDeleting,
  setError,
} from "@/store/slices/document";
import type {
  Document,
  CreateDocument,
  UpdateDocument,
} from "@/lib/api/validators/schemas/document";

// Fetcher function for SWR using api module
const fetcher = async (url: string) => {
  try {
    // Remove '/api/' prefix since api module already includes it
    const endpoint = url.startsWith("/api/") ? url.slice(5) : url;
    return await api.get(endpoint);
  } catch (error) {
    console.error("Fetcher error:", error);
    throw error;
  }
};

export function useDocuments() {
  const dispatch = useDispatch<AppDispatch>();

  // Redux selectors for operation states
  const currentDocument = useSelector(
    (state: RootState) => state.documents.currentDocument
  );
  const isCreating = useSelector(
    (state: RootState) => state.documents.isCreating
  );
  const isUpdating = useSelector(
    (state: RootState) => state.documents.isUpdating
  );
  const isDeleting = useSelector(
    (state: RootState) => state.documents.isDeleting
  );
  const error = useSelector((state: RootState) => state.documents.error);

  // SWR hooks for data fetching
  const {
    data: documentsData,
    error: documentsError,
    isLoading: isLoadingDocuments,
    mutate: mutateDocuments,
  } = useSWR("document/get", fetcher);

  const {
    data: statisticsData,
    error: statisticsError,
    isLoading: isLoadingStats,
    mutate: mutateStatistics,
  } = useSWR("document/statistics", fetcher);

  // Extract data from SWR responses
  const documents: Document[] = documentsData?.data?.documents || [];
  const statistics = statisticsData?.data || null;
  const isLoading = isLoadingDocuments;

  // Handle SWR errors
  const swrError = documentsError || statisticsError;
  if (swrError && !error) {
    dispatch(setError(swrError.message));
  }

  // API operations
  const create = useCallback(
    async (documentData: FormData) => {
      try {
        dispatch(setCreating(true));
        dispatch(clearError());

        // Check if documentData is FormData for file uploads, otherwise use regular post
        const result = await api.upload("document/upload", documentData);

        // Refresh data after creation
        await mutateDocuments();
        await mutateStatistics();

        return result;
      } catch (error) {
        const errorMessage =
          error instanceof Error ? error.message : "Failed to create document";
        dispatch(setError(errorMessage));
        throw error;
      } finally {
        dispatch(setCreating(false));
      }
    },
    [dispatch, mutateDocuments, mutateStatistics]
  );

  const update = useCallback(
    async (id: string, documentData: Partial<UpdateDocument>) => {
      try {
        dispatch(setUpdating(true));
        dispatch(clearError());

        const result = await api.put("document/update", {
          id,
          ...documentData,
        });

        // Update current document if it's the one being updated
        if (currentDocument?.id === id) {
          dispatch(setCurrentDocument(result));
        }

        // Refresh data after update
        await mutateDocuments();
        await mutateStatistics();

        return result;
      } catch (error) {
        const errorMessage =
          error instanceof Error ? error.message : "Failed to update document";
        dispatch(setError(errorMessage));
        throw error;
      } finally {
        dispatch(setUpdating(false));
      }
    },
    [dispatch, currentDocument, mutateDocuments, mutateStatistics]
  );

  const remove = useCallback(
    async (documentId: string) => {
      try {
        dispatch(setDeleting(true));
        dispatch(clearError());

        await api.delete("document/delete", { id: documentId });

        // Clear current document if it's the one being deleted
        if (currentDocument?.id === documentId) {
          dispatch(setCurrentDocument(null));
        }

        // Refresh data after deletion
        await mutateDocuments();
        await mutateStatistics();

        return { success: true };
      } catch (error) {
        const errorMessage =
          error instanceof Error ? error.message : "Failed to delete document";
        dispatch(setError(errorMessage));
        throw error;
      } finally {
        dispatch(setDeleting(false));
      }
    },
    [dispatch, currentDocument, mutateDocuments, mutateStatistics]
  );

  // Utility functions
  const fetchById = useCallback(
    async (documentId: string) => {
      try {
        const result = await api.get(`document/get?id=${documentId}`);
        if (result.success && !result.error) {
          dispatch(setCurrentDocument(result.data || result));
          return result.data || result;
        } else {
          throw new Error(result.message || "Failed to fetch document");
        }
      } catch (error) {
        const errorMessage =
          error instanceof Error ? error.message : "Failed to fetch document";
        dispatch(setError(errorMessage));
        throw error;
      }
    },
    [dispatch]
  );

  const clearErrorState = useCallback(() => {
    dispatch(clearError());
  }, [dispatch]);

  const setDocument = useCallback(
    (document: Document | null) => {
      dispatch(setCurrentDocument(document));
    },
    [dispatch]
  );

  const clearDocument = useCallback(() => {
    dispatch(clearCurrentDocument());
  }, [dispatch]);

  // Helper function to get file extension
  const getFileExtension = (fileName: string): string => {
    const extension = fileName.split(".").pop()?.toLowerCase();
    return extension || "pdf";
  };

  const getExtensionType = (fileName: string): string => {
    const extension = fileName.split(".").pop()?.toLowerCase();
    switch (extension) {
      case "pdf":
      case "doc":
      case "docx":
      case "xls":
      case "xlsx":
        return "application";
      case "jpg":
      case "jpeg":
      case "png":
      case "gif":
        return "image";
      default:
        return "other";
    }
  };

  // Download document
  const downloadDocument = useCallback(
    async (doc: any) => {
      try {
        // Fetch the document first to get the download URL or file data
        if (doc && doc.path) {
          const link = document.createElement("a");
          const extension = getFileExtension(doc.name);
          const extensionType = getExtensionType(doc.name);

          link.href = `data://${extensionType}/${extension},` + doc.path;
          link.target = "_blank";
          link.download = doc.name;

          document.body.appendChild(link);
          link.click();
          document.body.removeChild(link);
        } else {
          throw new Error("Document file URL not available");
        }
      } catch (error) {
        console.error("Failed to download document:", error);
        throw error;
      }
    },
    [fetchById]
  );

  return {
    // State
    documents,
    currentDocument,
    statistics,
    isLoading,
    isCreating,
    isUpdating,
    isDeleting,
    isLoadingStats,
    error,

    // Actions
    create,
    fetchById,
    update,
    remove,
    downloadDocument,
    clearError: clearErrorState,
    setCurrentDocument: setDocument,
    clearCurrentDocument: clearDocument,

    // SWR utilities
    mutateDocuments,
    mutateStatistics,

    // Computed values
    hasDocuments: documents.length > 0,
    documentsCount: documents.length,
    isAnyLoading:
      isLoading || isCreating || isUpdating || isDeleting || isLoadingStats,
  };
}

"use client";

import { useCallback, useEffect } from "react";
import { useSelector, useDispatch } from "react-redux";
import type { RootState, AppDispatch } from "@/store";
import {
  fetchRooms,
  fetchMessages,
  sendMessage,
  createRoom,
  updateUserState,
  fetchTypingUsers,
  fetchChatStatistics,
} from "@/store/actions/chat";
import {
  setCurrentRoom,
  clearError,
  addMessageOptimistic,
  setTypingUsers,
  setUserState,
  markMessagesAsRead,
} from "@/store/slices/chat";
import type {
  CreateRoom,
  CreateMessage,
  UserState,
} from "@/lib/api/validators/schemas/chat";
import { useChatNotifications } from "@/lib/notifications/chat-notifications";

export function useChat() {
  const dispatch = useDispatch<AppDispatch>();
  const { showMessageNotification, showUserStatusNotification } =
    useChatNotifications();

  const {
    rooms,
    currentRoomId,
    messages,
    typingUsers,
    userStates,
    statistics,
    isLoading,
    isLoadingMessages,
    isSendingMessage,
    isCreatingRoom,
    error,
  } = useSelector((state: RootState) => state.chat);

  const { user } = useSelector((state: RootState) => state.auth);

  // Get current room
  const currentRoom = rooms.find((room) => room.id === currentRoomId) || null;

  // Get messages for current room
  const currentMessages = currentRoomId ? messages[currentRoomId] || [] : [];

  // Get typing users for current room
  const currentTypingUsers = currentRoomId
    ? typingUsers[currentRoomId] || []
    : [];

  // Fetch all rooms for current user
  const fetchUserRooms = useCallback(async () => {
    try {
      await dispatch(fetchRooms(user?.id)).unwrap();
    } catch (error) {
      console.error("Failed to fetch rooms:", error);
      throw error;
    }
  }, [dispatch, user?.id]);

  // Fetch messages for a specific room
  const fetchRoomMessages = useCallback(
    async (roomId: string, limit = 50, offset = 0) => {
      try {
        await dispatch(fetchMessages({ roomId, limit, offset })).unwrap();
      } catch (error) {
        console.error("Failed to fetch messages:", error);
        throw error;
      }
    },
    [dispatch]
  );

  // Send a message
  const sendChatMessage = useCallback(
    async (content: string, roomId?: string) => {
      if (!user?.id || !content.trim()) return;

      const targetRoomId = roomId || currentRoomId;
      if (!targetRoomId) return;

      const messageData: CreateMessage = {
        content: content.trim(),
        sent_from: user.id,
        sent_to: targetRoomId,
        roomId: targetRoomId,
      };

      try {
        // Optimistic update
        const optimisticMessage = {
          id: `temp-${Date.now()}`,
          ...messageData,
          createdAt: new Date(),
          updatedAt: new Date(),
          sender: {
            id: user.id,
            name: user.name || "You",
            email: user.email || "",
            avatar: user.image || "",
          },
        };

        dispatch(addMessageOptimistic(optimisticMessage));

        // Send actual message
        const sentMessage = await dispatch(sendMessage(messageData)).unwrap();

        // Show notification for other users (in a real app, this would be handled by real-time events)
        // For demo purposes, we'll show a notification if the message is not from the current user
        if (
          sentMessage &&
          sentMessage.sender &&
          sentMessage.sender.id !== user.id
        ) {
          const room = rooms.find((r) => r.id === targetRoomId);
          if (room) {
            showMessageNotification(
              sentMessage.sender.name,
              sentMessage.content,
              room.name,
              () => {
                // Navigate to the room when notification is clicked
                if (currentRoomId !== targetRoomId) {
                  dispatch(setCurrentRoom(targetRoomId));
                }
              }
            );
          }
        }
      } catch (error) {
        console.error("Failed to send message:", error);
        throw error;
      }
    },
    [dispatch, user, currentRoomId]
  );

  // Create a new room
  const createChatRoom = useCallback(
    async (roomData: CreateRoom) => {
      try {
        const newRoom = await dispatch(createRoom(roomData)).unwrap();
        // Automatically switch to the new room
        dispatch(setCurrentRoom(newRoom.id));
        return newRoom;
      } catch (error) {
        console.error("Failed to create room:", error);
        throw error;
      }
    },
    [dispatch]
  );

  // Set current room
  const selectRoom = useCallback(
    (roomId: string | null) => {
      dispatch(setCurrentRoom(roomId));

      // Fetch messages for the selected room
      if (roomId) {
        fetchRoomMessages(roomId);
        // Mark messages as read
        dispatch(markMessagesAsRead(roomId));
      }
    },
    [dispatch, fetchRoomMessages]
  );

  // Update user state (online/offline/typing/away)
  const updateCurrentUserState = useCallback(
    async (state: UserState, roomId?: string) => {
      if (!user?.id) return;

      try {
        await dispatch(
          updateUserState({
            userId: user.id,
            state,
            roomId: roomId || currentRoomId || undefined,
          })
        ).unwrap();
      } catch (error) {
        console.error("Failed to update user state:", error);
      }
    },
    [dispatch, user?.id, currentRoomId]
  );

  // Start typing indicator
  const startTyping = useCallback(
    async (roomId?: string) => {
      const targetRoomId = roomId || currentRoomId;
      if (targetRoomId) {
        await updateCurrentUserState("typing", targetRoomId);
      }
    },
    [updateCurrentUserState, currentRoomId]
  );

  // Stop typing indicator
  const stopTyping = useCallback(
    async (roomId?: string) => {
      const targetRoomId = roomId || currentRoomId;
      if (targetRoomId) {
        await updateCurrentUserState("online", targetRoomId);
      }
    },
    [updateCurrentUserState, currentRoomId]
  );

  // Fetch statistics
  const fetchStats = useCallback(async () => {
    try {
      await dispatch(fetchChatStatistics()).unwrap();
    } catch (error) {
      console.error("Failed to fetch statistics:", error);
      throw error;
    }
  }, [dispatch]);

  // Clear error
  const clearChatError = useCallback(() => {
    dispatch(clearError());
  }, [dispatch]);

  // Initialize chat (fetch rooms and statistics)
  const initializeChat = useCallback(async () => {
    try {
      await Promise.all([
        dispatch(fetchRooms(user?.id)).unwrap(),
        dispatch(fetchChatStatistics()).unwrap(),
      ]);
    } catch (error) {
      console.error("Failed to initialize chat:", error);
    }
  }, [dispatch, user?.id]);

  // Auto-initialize when user is available
  useEffect(() => {
    if (user?.id) {
      initializeChat();
      // Set user as online
      updateCurrentUserState("online");
    }
  }, [user?.id, initializeChat, updateCurrentUserState]);

  // Cleanup on unmount
  useEffect(() => {
    return () => {
      if (user?.id) {
        // Set user as offline when component unmounts
        updateCurrentUserState("offline");
      }
    };
  }, [user?.id, updateCurrentUserState]);

  return {
    // State
    rooms,
    currentRoom,
    currentRoomId,
    messages: currentMessages,
    allMessages: messages,
    typingUsers: currentTypingUsers,
    allTypingUsers: typingUsers,
    userStates,
    statistics,
    isLoading,
    isLoadingMessages,
    isSendingMessage,
    isCreatingRoom,
    error,

    // Actions
    fetchRooms: fetchUserRooms,
    fetchMessages: fetchRoomMessages,
    sendMessage: sendChatMessage,
    createRoom: createChatRoom,
    selectRoom,
    updateUserState: updateCurrentUserState,
    startTyping,
    stopTyping,
    fetchStatistics: fetchStats,
    initializeChat,

    // Utility actions
    clearError: clearChatError,
    markAsRead: (roomId: string) => dispatch(markMessagesAsRead(roomId)),

    // Computed values
    hasRooms: rooms.length > 0,
    hasMessages: currentMessages.length > 0,
    isAnyLoading:
      isLoading || isLoadingMessages || isSendingMessage || isCreatingRoom,
    currentUser: user,
  };
}

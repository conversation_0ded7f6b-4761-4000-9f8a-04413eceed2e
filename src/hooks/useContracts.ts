"use client";

import { useCallback } from "react";
import { useSelector, useDispatch } from "react-redux";
import type { RootState, AppDispatch } from "@/store";
import {
  fetchContracts,
  fetchContract,
  createContract,
  updateContract,
  deleteContract,
  fetchContractStatistics,
} from "@/store/actions/contracts";
import {
  clearError,
  setCurrentContract,
  clearCurrentContract,
} from "@/store/slices/contract";
import type {
  Contract,
  CreateContract,
  UpdateContract,
} from "@/lib/api/validators/schemas/contract";

export function useContracts() {
  const dispatch = useDispatch<AppDispatch>();

  const { contracts, currentContract, statistics, isLoading, error } =
    useSelector((state: RootState) => state.contracts);

  // Fetch all contracts
  const fetchAllContracts = useCallback(async () => {
    try {
      await dispatch(fetchContracts()).unwrap();
    } catch (error) {
      console.error("Failed to fetch contracts:", error);
      throw error;
    }
  }, [dispatch]);

  // Fetch single contract
  const fetchSingleContract = useCallback(
    async (contractId: string) => {
      try {
        await dispatch(fetchContract(contractId)).unwrap();
      } catch (error) {
        console.error("Failed to fetch contract:", error);
        throw error;
      }
    },
    [dispatch]
  );

  // Create new contract
  const createNewContract = useCallback(
    async (contractData: CreateContract) => {
      try {
        const result = await dispatch(createContract(contractData)).unwrap();
        return result;
      } catch (error) {
        console.error("Failed to create contract:", error);
        throw error;
      }
    },
    [dispatch]
  );

  // Update existing contract
  const updateExistingContract = useCallback(
    async (contractId: string, contractData: Partial<UpdateContract>) => {
      try {
        const result = await dispatch(
          updateContract({
            id: contractId,
            data: contractData,
          })
        ).unwrap();
        return result;
      } catch (error) {
        console.error("Failed to update contract:", error);
        throw error;
      }
    },
    [dispatch]
  );

  // Delete contract
  const deleteExistingContract = useCallback(
    async (contractId: string) => {
      try {
        await dispatch(deleteContract(contractId)).unwrap();
      } catch (error) {
        console.error("Failed to delete contract:", error);
        throw error;
      }
    },
    [dispatch]
  );

  // Fetch contract statistics
  const fetchStats = useCallback(async () => {
    try {
      await dispatch(fetchContractStatistics()).unwrap();
    } catch (error) {
      console.error("Failed to fetch contract statistics:", error);
      throw error;
    }
  }, [dispatch]);

  // Clear error
  const clearContractError = useCallback(() => {
    dispatch(clearError());
  }, [dispatch]);

  // Set current contract
  const setContract = useCallback(
    (contract: Contract | null) => {
      dispatch(setCurrentContract(contract));
    },
    [dispatch]
  );

  // Clear current contract
  const clearContract = useCallback(() => {
    dispatch(clearCurrentContract());
  }, [dispatch]);

  // Auto-fetch contracts and statistics on hook initialization
  const initializeContracts = useCallback(async () => {
    try {
      await Promise.all([
        dispatch(fetchContracts()).unwrap(),
        dispatch(fetchContractStatistics()).unwrap(),
      ]);
    } catch (error) {
      console.error("Failed to initialize contracts:", error);
    }
  }, [dispatch]);

  return {
    // State
    contracts,
    currentContract,
    statistics,
    isLoading,
    error,

    // Actions
    fetchContracts: fetchAllContracts,
    fetchContract: fetchSingleContract,
    createContract: createNewContract,
    updateContract: updateExistingContract,
    deleteContract: deleteExistingContract,
    fetchStatistics: fetchStats,
    initializeContracts,

    // Utility actions
    clearError: clearContractError,
    setCurrentContract: setContract,
    clearCurrentContract: clearContract,
  };
}

import { useCallback } from "react";

import { redirect, useParams } from "next/navigation";
import { useSelector, useDispatch } from "react-redux";
import { useSession } from "next-auth/react";
import { RootState, AppDispatch } from "@/store";
import {
  refreshSession,
  loginUserWithOAuth,
  loginUserWithCredentials,
  registerUserWithCredentials,
  logoutUser,
} from "@/store/actions/auth";
import {
  selectAuth,
  selectRole,
  selectSession,
  selectUser,
  selectIsAuthenticated,
  selectIsLoading,
  selectIsDashboardView,
  updateUser,
  setDashboardView,
  toggleDashboardView,
  selectProfile,
  selectPersonlizedRoute,
} from "@/store/slices/auth";

export function useAuth() {
  const dispatch = useDispatch<AppDispatch>();
  const { data: nextAuthSession } = useSession();

  const { slug } = useParams();

  // Redux selectors
  const auth = useSelector((state: RootState) => selectAuth(state));
  const session = useSelector((state: RootState) => selectSession(state));
  const user = useSelector((state: RootState) => selectUser(state));
  const role = useSelector((state: RootState) => selectRole(state));
  const profile = useSelector((state: RootState) => selectProfile(state));
  const personalizedRoute = useSelector((state: RootState) =>
    selectPersonlizedRoute(state)
  );
  // Auth state
  const isAuthenticated = useSelector((state: RootState) =>
    selectIsAuthenticated(state)
  );
  const isLoading = useSelector((state: RootState) => selectIsLoading(state));
  const isDashboardView = useSelector((state: RootState) =>
    selectIsDashboardView(state)
  );

  const registerUser = async (
    name: string,
    email: string,
    password: string
  ) => {
    try {
      const result = await dispatch(
        registerUserWithCredentials({ name, email, password })
      );
      return result;
    } catch (error) {
      console.error("Registration error:", error);
      throw error;
    }
  };

  // Auth actions
  const loginWithOAuth = async (provider: string = "google") => {
    try {
      await dispatch(loginUserWithOAuth(provider));
      setDashboardViewState(true);
      redirect("/" + personalizedRoute);
    } catch (error) {
      console.error("Login error:", error);
      throw error;
    }
  };

  const loginWithCredentials = async (email: string, password: string) => {
    try {
      await dispatch(loginUserWithCredentials({ email, password }));
      setDashboardViewState(true);
      redirect("/" + personalizedRoute);
    } catch (error) {
      console.error("Login error:", error);
      throw error;
    }
  };

  const logout = async () => {
    try {
      const result = await dispatch(logoutUser());
      return result;
    } catch (error) {
      console.error("Logout error:", error);
      throw error;
    }
  };

  const exitDashboardView = () => {
    setDashboardViewState(false);
    redirect("/");
  };

  const refreshUserSession = async () => {
    try {
      const result = await dispatch(refreshSession());
      return result;
    } catch (error) {
      console.error("Refresh session error:", error);
      throw error;
    }
  };

  const updateUserProfile = (userData: Partial<typeof user>) => {
    dispatch(updateUser(userData));
  };

  function isCurrentRouteDashboardView() {
    if (!isDashboardView || !isAuthenticated) return;
    // If the current route is the dashboard view, do nothing
    if (personalizedRoute == slug) return;
    redirect("/" + personalizedRoute);
  }

  function routeAuthenticated() {
    if (!isAuthenticated) redirect("/signin");

    setDashboardView(true);
    redirect("/" + personalizedRoute);
  }

  // Dashboard view actions
  const setDashboardViewState = (isDashboard: boolean) => {
    dispatch(setDashboardView(isDashboard));
  };

  const toggleDashboardViewState = () => {
    dispatch(toggleDashboardView());
  };

  return {
    // State
    auth,
    user,
    role,
    session,
    profile,
    isLoading,
    isAuthenticated,
    isDashboardView,
    nextAuthSession,
    personalizedRoute,

    // Actions
    logout,
    registerUser,
    loginWithOAuth,
    updateUserProfile,
    exitDashboardView,
    routeAuthenticated,
    refreshUserSession,
    loginWithCredentials,
    setDashboardViewState,
    toggleDashboardViewState,
    isCurrentRouteDashboardView,
  };
}

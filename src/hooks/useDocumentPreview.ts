"use client";

import { useState } from "react";

export interface DocumentPreviewProps {
  isOpen: boolean;
  onClose: () => void;
  document: {
    uri: string;
    fileName: string;
    fileType?: string;
  } | null;
  title?: string;
  onDownload?: () => void;
  onPrint?: () => void;
  className?: string;
}

// Hook for document preview functionality
export function useDocumentPreview() {
  const [isOpen, setIsOpen] = useState(false);
  const [document, setDocument] =
    useState<DocumentPreviewProps["document"]>(null);

  const openPreview = (doc: NonNullable<DocumentPreviewProps["document"]>) => {
    setDocument(doc);
    setIsOpen(true);
  };

  const closePreview = () => {
    setIsOpen(false);
    setDocument(null);
  };

  return {
    isOpen,
    document,
    openPreview,
    closePreview,
  };
}

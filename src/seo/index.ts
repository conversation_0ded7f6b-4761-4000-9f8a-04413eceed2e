import type { Metadata } from "next";

export const metadata: Metadata = {
  title: "Underscor",
  description:
    "We are a team of passionate and innovative minds, dedicated to crafting enduring designs curating digital assets as simple as stacking blocks",

  // Basic metadata
  authors: [
    {
      name: "underscor's team: designed by <PERSON>, engineered by <PERSON>",
    },
  ],

  // Keywords for SEO
  keywords: [
    "Custom software development",
    "Software solutions",
    "Software development services",
    "Software engineering",
    "Enterprise software development",
    "Mobile app development",
    "Web application development",
    "Cloud-based software",
    "Software architecture",
    "Software consulting",
    "Agile software development",
    "User interface design (UI)",
    "User experience design (UX)",
    "Software maintenance",
    "Software integration",
    "Software testing and QA",
    "Backend development",
    "Frontend development",
    "Full-stack development",
    "Software deployment",
    "Digital product development",
    "Digital services",
    "Digital tools",
    "Digital brands",
    "Digital product solutions",
    "Digital marketing services",
    "Digital transformation solutions",
    "Digital strategy consulting",
    "Digital innovation",
    "Digital technology solutions",
    "Digital platform development",
    "Digital experience design",
    "Digital content management",
    "Digital analytics solutions",
    "Digital advertising services",
    "Digital branding services",
    "Digital commerce solutions",
    "Digital customer experience",
    "Digital user engagement",
    "Digital presence management",
    "Digital product optimization",
  ],

  // Robots and indexing
  robots: {
    index: true,
    follow: true,
  },

  // Open Graph metadata
  openGraph: {
    type: "website",
    url: "https://www.underscor.io",
    title: "Underscor",
    description:
      "We are a team of passionate and innovative minds, dedicated to crafting enduring designs curating digital assets as simple as stacking blocks",
    images: [
      {
        url: "https://opengraph.b-cdn.net/production/documents/624b2237-84ac-4f0e-a2d0-e996a9732001.png?token=AGRSObROQHd1D8HNni7Ju9UhJ69S9W4vMDJ_ONqutiY&height=675&width=1200&expires=33244335092",
        width: 1200,
        height: 675,
        alt: "Underscor",
      },
    ],
    locale: "en_US",
    siteName: "Underscor",
  },

  // Twitter metadata
  twitter: {
    card: "summary_large_image",
    site: "@underscor",
    creator: "@underscor",
    title: "Underscor",
    description:
      "We are a team of passionate and innovative minds, dedicated to crafting enduring designs curating digital assets as simple as stacking blocks",
    images: [
      "https://opengraph.b-cdn.net/production/documents/624b2237-84ac-4f0e-a2d0-e996a9732001.png?token=AGRSObROQHd1D8HNni7Ju9UhJ69S9W4vMDJ_ONqutiY&height=675&width=1200&expires=33244335092",
    ],
  },

  // Icons and manifest
  icons: {
    icon: [
      {
        url: "/favicons/favicon-16x16.png",
        sizes: "16x16",
        type: "image/png",
      },
      {
        url: "/favicons/favicon-32x32.png",
        sizes: "32x32",
        type: "image/png",
      },
    ],
    apple: [
      {
        url: "/favicons/apple-touch-icon.png",
        sizes: "180x180",
        type: "image/png",
      },
    ],
  },

  // Manifest
  manifest: "/site.webmanifest",

  // Viewport
  viewport: {
    width: "device-width",
    initialScale: 1.0,
  },

  // Additional metadata
  other: {
    "revisit-after": "1 days",
  },
};

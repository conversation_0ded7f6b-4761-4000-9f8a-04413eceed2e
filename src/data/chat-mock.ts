import { Room, Message, MemberWithState, UserState } from "@/lib/api/validators/schemas/chat";

export interface ChatStatistics {
  totalRooms: number;
  totalMessages: number;
  activeUsers: number;
  onlineUsers: number;
}

// Mock users for chat
export const mockChatUsers = [
  {
    id: "user-1",
    name: "<PERSON><PERSON>",
    email: "<EMAIL>",
    avatar: "/avatars/dianne-russell.jpg",
  },
  {
    id: "user-2", 
    name: "<PERSON>",
    email: "<EMAIL>",
    avatar: "/avatars/ronald-richards.jpg",
  },
  {
    id: "user-3",
    name: "<PERSON>",
    email: "<EMAIL>", 
    avatar: "/avatars/devon-lane.jpg",
  },
  {
    id: "user-4",
    name: "<PERSON><PERSON>",
    email: "<EMAIL>",
    avatar: "/avatars/marielle-wigington.jpg",
  },
  {
    id: "user-5",
    name: "<PERSON>",
    email: "<EMAIL>",
    avatar: "/avatars/marvin-mckinney.jpg",
  },
  {
    id: "user-6",
    name: "<PERSON>lene <PERSON>",
    email: "<EMAIL>",
    avatar: "/avatars/darlene-robertson.jpg",
  },
  {
    id: "user-7",
    name: "<PERSON>tin <PERSON>",
    email: "<EMAIL>",
    avatar: "/avatars/kristin-watson.jpg",
  },
  {
    id: "user-8",
    name: "<PERSON> <PERSON>",
    email: "<EMAIL>",
    avatar: "/avatars/jenny-wilson.jpg",
  },
  {
    id: "user-9",
    name: "Bessie Cooper",
    email: "<EMAIL>",
    avatar: "/avatars/bessie-cooper.jpg",
  },
];

// Mock rooms
export const mockRooms: Room[] = [
  {
    id: "room-1",
    name: "Project Alpha Team",
    about: "Main discussion for Project Alpha development",
    contractId: "contract-1",
    createdAt: new Date("2024-01-15T10:00:00.000Z"),
    updatedAt: new Date("2024-01-20T14:30:00.000Z"),
  },
  {
    id: "room-2", 
    name: "Design Review",
    about: "UI/UX design discussions and feedback",
    contractId: "contract-1",
    createdAt: new Date("2024-01-16T09:00:00.000Z"),
    updatedAt: new Date("2024-01-20T16:45:00.000Z"),
  },
  {
    id: "room-3",
    name: "Client Communications",
    about: "Direct communication with client stakeholders",
    contractId: "contract-2",
    createdAt: new Date("2024-01-17T11:00:00.000Z"),
    updatedAt: new Date("2024-01-20T13:20:00.000Z"),
  },
  {
    id: "room-4",
    name: "Development Updates",
    about: "Daily standups and development progress",
    contractId: "contract-1",
    createdAt: new Date("2024-01-18T08:00:00.000Z"),
    updatedAt: new Date("2024-01-20T17:10:00.000Z"),
  },
];

// Mock members with states
export const mockMembers: MemberWithState[] = [
  {
    id: "member-1",
    accountId: "user-1",
    roomId: "room-1",
    state: "online",
    lastSeen: new Date("2024-01-20T17:30:00.000Z"),
  },
  {
    id: "member-2",
    accountId: "user-2", 
    roomId: "room-1",
    state: "offline",
    lastSeen: new Date("2024-01-20T15:45:00.000Z"),
  },
  {
    id: "member-3",
    accountId: "user-3",
    roomId: "room-1",
    state: "online",
    lastSeen: new Date("2024-01-20T17:25:00.000Z"),
  },
  {
    id: "member-4",
    accountId: "user-4",
    roomId: "room-2",
    state: "away",
    lastSeen: new Date("2024-01-20T16:30:00.000Z"),
  },
  {
    id: "member-5",
    accountId: "user-5",
    roomId: "room-2",
    state: "online",
    lastSeen: new Date("2024-01-20T17:20:00.000Z"),
  },
  {
    id: "member-6",
    accountId: "user-6",
    roomId: "room-3",
    state: "offline",
    lastSeen: new Date("2024-01-20T14:15:00.000Z"),
  },
  {
    id: "member-7",
    accountId: "user-7",
    roomId: "room-3",
    state: "online",
    lastSeen: new Date("2024-01-20T17:15:00.000Z"),
  },
  {
    id: "member-8",
    accountId: "user-8",
    roomId: "room-4",
    state: "typing",
    lastSeen: new Date("2024-01-20T17:30:00.000Z"),
  },
  {
    id: "member-9",
    accountId: "user-9",
    roomId: "room-4",
    state: "online",
    lastSeen: new Date("2024-01-20T17:28:00.000Z"),
  },
];

// Mock messages
export const mockMessages: Message[] = [
  {
    id: "msg-1",
    content: "Just finalizing the budget proposal for next quarter. It's taking longer than expected because I keep second-guessing myself.",
    sent_from: "user-1",
    sent_to: "room-1",
    roomId: "room-1",
    associations: [],
    createdAt: new Date("2024-01-20T09:30:00.000Z"),
    updatedAt: new Date("2024-01-20T09:30:00.000Z"),
  },
  {
    id: "msg-2",
    content: "I totally get that. I'm working on a presentation for the new project, and I keep thinking I'm missing something. I should've asked more questions when we first discussed it.",
    sent_from: "user-2",
    sent_to: "room-1", 
    roomId: "room-1",
    associations: [],
    createdAt: new Date("2024-01-20T10:15:00.000Z"),
    updatedAt: new Date("2024-01-20T10:15:00.000Z"),
  },
  {
    id: "msg-3",
    content: "Yeah, I'm sure we've all been there.",
    sent_from: "user-3",
    sent_to: "room-1",
    roomId: "room-1", 
    associations: [],
    createdAt: new Date("2024-01-20T10:45:00.000Z"),
    updatedAt: new Date("2024-01-20T10:45:00.000Z"),
  },
  {
    id: "msg-4",
    content: "Honestly, sometimes I just need to step away and look at things with fresh eyes. Have you tried that?",
    sent_from: "user-1",
    sent_to: "room-1",
    roomId: "room-1",
    associations: [],
    createdAt: new Date("2024-01-20T11:20:00.000Z"),
    updatedAt: new Date("2024-01-20T11:20:00.000Z"),
  },
  {
    id: "msg-5",
    content: "That's a good idea. 👍",
    sent_from: "user-2",
    sent_to: "room-1",
    roomId: "room-1",
    associations: [],
    createdAt: new Date("2024-01-20T11:25:00.000Z"),
    updatedAt: new Date("2024-01-20T11:25:00.000Z"),
  },
  {
    id: "msg-6",
    content: "Please fill this form",
    sent_from: "user-3",
    sent_to: "room-1",
    roomId: "room-1",
    associations: [{ type: "form", id: "form-1", title: "Project Requirements Form" }],
    createdAt: new Date("2024-01-20T12:00:00.000Z"),
    updatedAt: new Date("2024-01-20T12:00:00.000Z"),
  },
  {
    id: "msg-7",
    content: "Done ✅",
    sent_from: "user-1",
    sent_to: "room-1",
    roomId: "room-1",
    associations: [],
    createdAt: new Date("2024-01-20T12:30:00.000Z"),
    updatedAt: new Date("2024-01-20T12:30:00.000Z"),
  },
];

// Mock statistics
export const mockChatStatistics: ChatStatistics = {
  totalRooms: mockRooms.length,
  totalMessages: mockMessages.length,
  activeUsers: mockMembers.filter(m => m.state !== "offline").length,
  onlineUsers: mockMembers.filter(m => m.state === "online").length,
};

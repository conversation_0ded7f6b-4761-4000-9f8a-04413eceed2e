export interface Contract {
  id: string;
  title: string;
  description: string;
  status: "draft" | "active" | "completed" | "terminated" | "expired";
  clientName: string;
  contractValue: number;
  startDate: string;
  endDate: string;
  createdDate: string;
  lastModified: string;
  proposalId?: string;
}

export const mockContracts: Contract[] = [
  {
    id: "contract-1",
    title: "Website Development Contract",
    description: "Complete website development and design services for ABC Corp",
    status: "active",
    clientName: "ABC Corporation",
    contractValue: 25000,
    startDate: "2024-01-15T00:00:00.000Z",
    endDate: "2024-04-15T23:59:59.999Z",
    createdDate: "2024-01-10T10:00:00.000Z",
    lastModified: "2024-01-15T14:30:00.000Z",
    proposalId: "proposal-1",
  },
  {
    id: "contract-2",
    title: "Mobile App Development",
    description: "iOS and Android mobile application development",
    status: "completed",
    clientName: "Tech Startup Inc",
    contractValue: 45000,
    startDate: "2023-10-01T00:00:00.000Z",
    endDate: "2024-01-31T23:59:59.999Z",
    createdDate: "2023-09-25T09:00:00.000Z",
    lastModified: "2024-02-01T16:45:00.000Z",
    proposalId: "proposal-2",
  },
  {
    id: "contract-3",
    title: "E-commerce Platform",
    description: "Custom e-commerce platform with payment integration",
    status: "draft",
    clientName: "Retail Solutions Ltd",
    contractValue: 75000,
    startDate: "2024-03-01T00:00:00.000Z",
    endDate: "2024-08-31T23:59:59.999Z",
    createdDate: "2024-02-20T11:15:00.000Z",
    lastModified: "2024-02-22T13:20:00.000Z",
    proposalId: "proposal-3",
  },
  {
    id: "contract-4",
    title: "API Integration Services",
    description: "Third-party API integration and data synchronization",
    status: "active",
    clientName: "Data Systems Co",
    contractValue: 15000,
    startDate: "2024-02-01T00:00:00.000Z",
    endDate: "2024-05-01T23:59:59.999Z",
    createdDate: "2024-01-25T14:00:00.000Z",
    lastModified: "2024-02-01T10:30:00.000Z",
    proposalId: "proposal-4",
  },
  {
    id: "contract-5",
    title: "Legacy System Migration",
    description: "Migration of legacy systems to modern cloud infrastructure",
    status: "terminated",
    clientName: "Enterprise Corp",
    contractValue: 120000,
    startDate: "2023-08-01T00:00:00.000Z",
    endDate: "2024-02-01T23:59:59.999Z",
    createdDate: "2023-07-15T16:30:00.000Z",
    lastModified: "2023-12-15T09:45:00.000Z",
    proposalId: "proposal-5",
  },
];

export interface ContractStatistics {
  total: number;
  active: number;
  completed: number;
  draft: number;
  terminated: number;
  totalValue: number;
  averageValue: number;
}

export const mockContractStatistics: ContractStatistics = {
  total: mockContracts.length,
  active: mockContracts.filter(c => c.status === "active").length,
  completed: mockContracts.filter(c => c.status === "completed").length,
  draft: mockContracts.filter(c => c.status === "draft").length,
  terminated: mockContracts.filter(c => c.status === "terminated").length,
  totalValue: mockContracts.reduce((sum, c) => sum + c.contractValue, 0),
  averageValue: mockContracts.reduce((sum, c) => sum + c.contractValue, 0) / mockContracts.length,
};

export interface Milestone {
  index: number;
  amount: number;
  description: string;
}

export interface Proposal {
  id: string;
  name: string;
  description: string;
  client: string;
  status: 'draft' | 'pending' | 'approved' | 'rejected' | 'completed';
  budgetType: 'fixed' | 'milestone';
  fixedBudget?: number;
  milestones?: Milestone[];
  totalBudget: number;
  duration: number; // in weeks
  createdDate: string;
  lastModified: string;
  attachments: string[];
  agreedToTerms: boolean;
}

export interface ProposalStats {
  total: number;
  pending: number;
  approved: number;
  rejected: number;
  totalValue: number;
}

// Mock Data
export const mockProposalStats: ProposalStats = {
  total: 24,
  pending: 8,
  approved: 12,
  rejected: 4,
  totalValue: 156750,
};

export const mockProposals: Proposal[] = [
  {
    id: "1",
    name: "E-commerce Website Redesign",
    description: "Complete redesign of the existing e-commerce platform with modern UI/UX and improved performance.",
    client: "TechCorp Solutions",
    status: "approved",
    budgetType: "milestone",
    milestones: [
      { index: 1, amount: 5000, description: "Initial design mockups and wireframes" },
      { index: 2, amount: 8000, description: "Frontend development and integration" },
      { index: 3, amount: 3000, description: "Testing and deployment" },
    ],
    totalBudget: 16000,
    duration: 8,
    createdDate: "2024-01-15",
    lastModified: "2024-01-20",
    attachments: ["requirements.pdf", "wireframes.fig"],
    agreedToTerms: true,
  },
  {
    id: "2",
    name: "Mobile App Development",
    description: "Native iOS and Android app for customer management system.",
    client: "StartupXYZ",
    status: "pending",
    budgetType: "fixed",
    fixedBudget: 25000,
    totalBudget: 25000,
    duration: 12,
    createdDate: "2024-01-18",
    lastModified: "2024-01-22",
    attachments: ["app_specs.pdf"],
    agreedToTerms: true,
  },
  {
    id: "3",
    name: "Brand Identity Package",
    description: "Complete brand identity including logo, color palette, and brand guidelines.",
    client: "Creative Agency Ltd",
    status: "draft",
    budgetType: "milestone",
    milestones: [
      { index: 1, amount: 2500, description: "Logo concepts and initial designs" },
      { index: 2, amount: 1500, description: "Brand guidelines and final deliverables" },
    ],
    totalBudget: 4000,
    duration: 4,
    createdDate: "2024-01-20",
    lastModified: "2024-01-23",
    attachments: ["brand_brief.pdf"],
    agreedToTerms: false,
  },
  {
    id: "4",
    name: "Database Migration Project",
    description: "Migration of legacy database to modern cloud infrastructure with data optimization.",
    client: "Enterprise Corp",
    status: "rejected",
    budgetType: "fixed",
    fixedBudget: 18000,
    totalBudget: 18000,
    duration: 6,
    createdDate: "2024-01-10",
    lastModified: "2024-01-25",
    attachments: ["migration_plan.pdf", "current_schema.sql"],
    agreedToTerms: true,
  },
  {
    id: "5",
    name: "Marketing Website",
    description: "Modern marketing website with CMS integration and SEO optimization.",
    client: "Digital Marketing Pro",
    status: "completed",
    budgetType: "milestone",
    milestones: [
      { index: 1, amount: 3000, description: "Design and content strategy" },
      { index: 2, amount: 4000, description: "Development and CMS setup" },
      { index: 3, amount: 1000, description: "SEO optimization and launch" },
    ],
    totalBudget: 8000,
    duration: 5,
    createdDate: "2023-12-15",
    lastModified: "2024-01-15",
    attachments: ["content_strategy.pdf"],
    agreedToTerms: true,
  },
  {
    id: "6",
    name: "API Integration Service",
    description: "Custom API development for third-party service integrations.",
    client: "SaaS Company",
    status: "pending",
    budgetType: "fixed",
    fixedBudget: 12000,
    totalBudget: 12000,
    duration: 7,
    createdDate: "2024-01-22",
    lastModified: "2024-01-24",
    attachments: ["api_documentation.pdf"],
    agreedToTerms: true,
  },
];

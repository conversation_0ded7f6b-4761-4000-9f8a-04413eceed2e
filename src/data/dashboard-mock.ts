export interface UserProfile {
  id: string;
  name: string;
  position: string;
  email: string;
  avatar: string;
  joinedDate: string;
  yearsInCompany: number;
  salary: number;
  projectsInProgress: number;
  projectsCompleted: number;
}

export interface ActivityData {
  weeklyActivity: number;
  design: number;
  communication: number;
}

export interface WorkingFormat {
  remote: number;
  hybrid: number;
}

export interface TimeEntry {
  id: string;
  project: string;
  time: string;
  status: 'active' | 'completed';
}

export interface CalendarEvent {
  id: string;
  time: string;
  title: string;
  type: string;
  hasVideo: boolean;
}

export interface CompanyInfo {
  name: string;
  logo: string;
  members: number;
  isPremium: boolean;
}

// Mock Data
export const mockUserProfile: UserProfile = {
  id: "1",
  name: "<PERSON>",
  position: "Senior Product Designer",
  email: "<EMAIL>",
  avatar: "/api/placeholder/300/300",
  joinedDate: "Jan 12, 2023",
  yearsInCompany: 2,
  salary: 6870,
  projectsInProgress: 3,
  projectsCompleted: 34,
};

export const mockActivityData: ActivityData = {
  weeklyActivity: 72,
  design: 72,
  communication: 28,
};

export const mockWorkingFormat: WorkingFormat = {
  remote: 42,
  hybrid: 58,
};

export const mockTimeEntries: TimeEntry[] = [
  {
    id: "1",
    project: "UX Researching",
    time: "01:34:07",
    status: "active",
  },
  {
    id: "2",
    project: "Wireframing",
    time: "02:15:30",
    status: "completed",
  },
];

export const mockCalendarEvents: CalendarEvent[] = [
  {
    id: "1",
    time: "12:00",
    title: "One to one",
    type: "CEO",
    hasVideo: true,
  },
  {
    id: "2",
    time: "13:40",
    title: "Sales meeting",
    type: "Creative director",
    hasVideo: true,
  },
  {
    id: "3",
    time: "17:00",
    title: "Design review",
    type: "Art Director",
    hasVideo: true,
  },
];

export const mockCompanyInfo: CompanyInfo = {
  name: "BlueWave IT",
  logo: "/api/placeholder/40/40",
  members: 35,
  isPremium: true,
};

// Calendar data for January 2025
export const mockCalendarData = {
  currentMonth: "January 2025",
  days: [
    { date: 27, isCurrentMonth: false },
    { date: 28, isCurrentMonth: false },
    { date: 29, isCurrentMonth: false },
    { date: 30, isCurrentMonth: false },
    { date: 1, isCurrentMonth: true },
    { date: 2, isCurrentMonth: true },
    { date: 3, isCurrentMonth: true },
    { date: 4, isCurrentMonth: true },
    { date: 5, isCurrentMonth: true },
    { date: 6, isCurrentMonth: true },
    { date: 7, isCurrentMonth: true },
    { date: 8, isCurrentMonth: true },
    { date: 9, isCurrentMonth: true },
    { date: 10, isCurrentMonth: true },
    { date: 11, isCurrentMonth: true },
    { date: 12, isCurrentMonth: true, isToday: true },
    { date: 13, isCurrentMonth: true },
    { date: 14, isCurrentMonth: true },
    { date: 15, isCurrentMonth: true },
    { date: 16, isCurrentMonth: true },
    { date: 17, isCurrentMonth: true },
    { date: 18, isCurrentMonth: true },
    { date: 19, isCurrentMonth: true },
    { date: 20, isCurrentMonth: true },
    { date: 21, isCurrentMonth: true },
    { date: 22, isCurrentMonth: true },
    { date: 23, isCurrentMonth: true },
    { date: 24, isCurrentMonth: true },
    { date: 25, isCurrentMonth: true },
    { date: 26, isCurrentMonth: true },
    { date: 27, isCurrentMonth: true },
    { date: 28, isCurrentMonth: true },
    { date: 29, isCurrentMonth: true },
    { date: 30, isCurrentMonth: true },
    { date: 31, isCurrentMonth: true },
  ],
};

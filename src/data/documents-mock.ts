export interface Document {
  id: string;
  name: string;
  path: string;
  file_type: string;
  size: string;
  status: "created" | "submitted" | "received" | "negotiating" | "agreed" | "inprogress" | "reviewing" | "completed";
  category: string;
  association_entity: string;
  association_id: string;
  proposalId?: string;
  createdAt: string;
  updatedAt: string;
}

export interface DocumentStatistics {
  total: number;
  created: number;
  submitted: number;
  received: number;
  negotiating: number;
  agreed: number;
  inprogress: number;
  reviewing: number;
  completed: number;
  totalSize: number;
  averageSize: number;
}

export const mockDocuments: Document[] = [
  {
    id: "doc-1",
    name: "Project Requirements.pdf",
    path: "/documents/project-requirements.pdf",
    file_type: "application/pdf",
    size: "2048576", // 2MB in bytes
    status: "completed",
    category: "requirements",
    association_entity: "contract",
    association_id: "contract-1",
    proposalId: "proposal-1",
    createdAt: "2024-01-15T10:00:00.000Z",
    updatedAt: "2024-01-20T14:30:00.000Z",
  },
  {
    id: "doc-2",
    name: "Technical Specifications.docx",
    path: "/documents/technical-specifications.docx",
    file_type: "application/vnd.openxmlformats-officedocument.wordprocessingml.document",
    size: "1536000", // 1.5MB in bytes
    status: "reviewing",
    category: "specifications",
    association_entity: "proposal",
    association_id: "proposal-2",
    createdAt: "2024-01-18T09:15:00.000Z",
    updatedAt: "2024-01-22T16:45:00.000Z",
  },
  {
    id: "doc-3",
    name: "Design Mockups.fig",
    path: "/documents/design-mockups.fig",
    file_type: "application/octet-stream",
    size: "5242880", // 5MB in bytes
    status: "inprogress",
    category: "design",
    association_entity: "contract",
    association_id: "contract-2",
    proposalId: "proposal-3",
    createdAt: "2024-01-20T11:30:00.000Z",
    updatedAt: "2024-01-25T13:20:00.000Z",
  },
  {
    id: "doc-4",
    name: "User Manual.pdf",
    path: "/documents/user-manual.pdf",
    file_type: "application/pdf",
    size: "3145728", // 3MB in bytes
    status: "agreed",
    category: "documentation",
    association_entity: "contract",
    association_id: "contract-3",
    createdAt: "2024-01-22T14:00:00.000Z",
    updatedAt: "2024-01-24T10:15:00.000Z",
  },
  {
    id: "doc-5",
    name: "API Documentation.md",
    path: "/documents/api-documentation.md",
    file_type: "text/markdown",
    size: "512000", // 500KB in bytes
    status: "submitted",
    category: "documentation",
    association_entity: "proposal",
    association_id: "proposal-4",
    createdAt: "2024-01-25T08:45:00.000Z",
    updatedAt: "2024-01-26T12:30:00.000Z",
  },
  {
    id: "doc-6",
    name: "Contract Agreement.pdf",
    path: "/documents/contract-agreement.pdf",
    file_type: "application/pdf",
    size: "1048576", // 1MB in bytes
    status: "negotiating",
    category: "legal",
    association_entity: "contract",
    association_id: "contract-4",
    proposalId: "proposal-5",
    createdAt: "2024-01-28T15:20:00.000Z",
    updatedAt: "2024-01-30T09:10:00.000Z",
  },
  {
    id: "doc-7",
    name: "Test Results.xlsx",
    path: "/documents/test-results.xlsx",
    file_type: "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet",
    size: "2097152", // 2MB in bytes
    status: "received",
    category: "testing",
    association_entity: "contract",
    association_id: "contract-5",
    createdAt: "2024-02-01T10:30:00.000Z",
    updatedAt: "2024-02-02T14:45:00.000Z",
  },
  {
    id: "doc-8",
    name: "Deployment Guide.pdf",
    path: "/documents/deployment-guide.pdf",
    file_type: "application/pdf",
    size: "1572864", // 1.5MB in bytes
    status: "created",
    category: "documentation",
    association_entity: "proposal",
    association_id: "proposal-6",
    createdAt: "2024-02-03T13:15:00.000Z",
    updatedAt: "2024-02-03T13:15:00.000Z",
  },
];

export const mockDocumentStatistics: DocumentStatistics = {
  total: 8,
  created: 1,
  submitted: 1,
  received: 1,
  negotiating: 1,
  agreed: 1,
  inprogress: 1,
  reviewing: 1,
  completed: 1,
  totalSize: 17203200, // Total size in bytes (approximately 16.4MB)
  averageSize: 2150400, // Average size in bytes (approximately 2.05MB)
};

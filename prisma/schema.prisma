generator client {
  provider = "prisma-client-js"
}

datasource db {
  provider = "postgresql"
  url      = env("DATABASE_URL")
}

model categories {
  id                            Int                             @id @default(autoincrement())
  name                          String?                         @db.VarChar
  updated_at                    DateTime                        @default(now()) @db.Timestamptz(3)
  created_at                    DateTime                        @default(now()) @db.Timestamptz(3)
  payload_locked_documents_rels payload_locked_documents_rels[]
  products_rels                 products_rels[]
  projects_rels                 projects_rels[]

  @@index([created_at])
  @@index([updated_at])
}

model clients {
  id                            Int                             @id @default(autoincrement())
  email                         String                          @unique(map: "clients_email_idx") @default("blank") @db.VarChar
  updated_at                    DateTime                        @default(now()) @db.Timestamptz(3)
  created_at                    DateTime                        @default(now()) @db.Timestamptz(3)
  clients_interest              clients_interest[]
  clients_rels                  clients_rels[]
  payload_locked_documents_rels payload_locked_documents_rels[]

  @@index([created_at])
  @@index([updated_at])
}

model clients_interest {
  order     Int
  parent_id Int
  value     enum_clients_interest?
  id        Int                    @id @default(autoincrement())
  clients   clients                @relation(fields: [parent_id], references: [id], onDelete: Cascade, onUpdate: NoAction, map: "clients_interest_parent_fk")

  @@index([order])
  @@index([parent_id], map: "clients_interest_parent_idx")
}

model clients_rels {
  id          Int       @id @default(autoincrement())
  order       Int?
  parent_id   Int
  path        String    @db.VarChar
  projects_id Int?
  clients     clients   @relation(fields: [parent_id], references: [id], onDelete: Cascade, onUpdate: NoAction, map: "clients_rels_parent_fk")
  projects    projects? @relation(fields: [projects_id], references: [id], onDelete: Cascade, onUpdate: NoAction, map: "clients_rels_projects_fk")

  @@index([order])
  @@index([parent_id], map: "clients_rels_parent_idx")
  @@index([path])
  @@index([projects_id])
}

model media {
  id                            Int                             @id @default(autoincrement())
  alt                           String?                         @db.VarChar
  updated_at                    DateTime                        @default(now()) @db.Timestamptz(3)
  created_at                    DateTime                        @default(now()) @db.Timestamptz(3)
  url                           String?                         @db.VarChar
  thumbnail_u_r_l               String?                         @db.VarChar
  filename                      String?                         @unique(map: "media_filename_idx") @db.VarChar
  mime_type                     String?                         @db.VarChar
  filesize                      Decimal?                        @db.Decimal
  width                         Decimal?                        @db.Decimal
  height                        Decimal?                        @db.Decimal
  focal_x                       Decimal?                        @db.Decimal
  focal_y                       Decimal?                        @db.Decimal
  sizes_thumbnail_url           String?                         @db.VarChar
  sizes_thumbnail_width         Decimal?                        @db.Decimal
  sizes_thumbnail_height        Decimal?                        @db.Decimal
  sizes_thumbnail_mime_type     String?                         @db.VarChar
  sizes_thumbnail_filesize      Decimal?                        @db.Decimal
  sizes_thumbnail_filename      String?                         @db.VarChar
  sizes_card_url                String?                         @db.VarChar
  sizes_card_width              Decimal?                        @db.Decimal
  sizes_card_height             Decimal?                        @db.Decimal
  sizes_card_mime_type          String?                         @db.VarChar
  sizes_card_filesize           Decimal?                        @db.Decimal
  sizes_card_filename           String?                         @db.VarChar
  sizes_tablet_url              String?                         @db.VarChar
  sizes_tablet_width            Decimal?                        @db.Decimal
  sizes_tablet_height           Decimal?                        @db.Decimal
  sizes_tablet_mime_type        String?                         @db.VarChar
  sizes_tablet_filesize         Decimal?                        @db.Decimal
  sizes_tablet_filename         String?                         @db.VarChar
  payload_locked_documents_rels payload_locked_documents_rels[]
  products                      products[]
  products_gallery              products_gallery[]
  projects                      projects[]
  projects_gallery              projects_gallery[]

  @@index([created_at])
  @@index([sizes_card_filename], map: "media_sizes_card_sizes_card_filename_idx")
  @@index([sizes_tablet_filename], map: "media_sizes_tablet_sizes_tablet_filename_idx")
  @@index([sizes_thumbnail_filename], map: "media_sizes_thumbnail_sizes_thumbnail_filename_idx")
  @@index([updated_at])
}

model payload_locked_documents {
  id                            Int                             @id @default(autoincrement())
  global_slug                   String?                         @db.VarChar
  updated_at                    DateTime                        @default(now()) @db.Timestamptz(3)
  created_at                    DateTime                        @default(now()) @db.Timestamptz(3)
  payload_locked_documents_rels payload_locked_documents_rels[]

  @@index([created_at])
  @@index([global_slug])
  @@index([updated_at])
}

model payload_locked_documents_rels {
  id                       Int                      @id @default(autoincrement())
  order                    Int?
  parent_id                Int
  path                     String                   @db.VarChar
  users_id                 Int?
  clients_id               Int?
  projects_id              Int?
  products_id              Int?
  categories_id            Int?
  media_id                 Int?
  categories               categories?              @relation(fields: [categories_id], references: [id], onDelete: Cascade, onUpdate: NoAction, map: "payload_locked_documents_rels_categories_fk")
  clients                  clients?                 @relation(fields: [clients_id], references: [id], onDelete: Cascade, onUpdate: NoAction, map: "payload_locked_documents_rels_clients_fk")
  media                    media?                   @relation(fields: [media_id], references: [id], onDelete: Cascade, onUpdate: NoAction, map: "payload_locked_documents_rels_media_fk")
  payload_locked_documents payload_locked_documents @relation(fields: [parent_id], references: [id], onDelete: Cascade, onUpdate: NoAction, map: "payload_locked_documents_rels_parent_fk")
  products                 products?                @relation(fields: [products_id], references: [id], onDelete: Cascade, onUpdate: NoAction, map: "payload_locked_documents_rels_products_fk")
  projects                 projects?                @relation(fields: [projects_id], references: [id], onDelete: Cascade, onUpdate: NoAction, map: "payload_locked_documents_rels_projects_fk")
  users                    users?                   @relation(fields: [users_id], references: [id], onDelete: Cascade, onUpdate: NoAction, map: "payload_locked_documents_rels_users_fk")

  @@index([categories_id])
  @@index([clients_id])
  @@index([media_id])
  @@index([order])
  @@index([parent_id], map: "payload_locked_documents_rels_parent_idx")
  @@index([path])
  @@index([products_id])
  @@index([projects_id])
  @@index([users_id])
}

model payload_migrations {
  id         Int      @id @default(autoincrement())
  name       String?  @db.VarChar
  batch      Decimal? @db.Decimal
  updated_at DateTime @default(now()) @db.Timestamptz(3)
  created_at DateTime @default(now()) @db.Timestamptz(3)

  @@index([created_at])
  @@index([updated_at])
}

model payload_preferences {
  id                       Int                        @id @default(autoincrement())
  key                      String?                    @db.VarChar
  value                    Json?
  updated_at               DateTime                   @default(now()) @db.Timestamptz(3)
  created_at               DateTime                   @default(now()) @db.Timestamptz(3)
  payload_preferences_rels payload_preferences_rels[]

  @@index([created_at])
  @@index([key])
  @@index([updated_at])
}

model payload_preferences_rels {
  id                  Int                 @id @default(autoincrement())
  order               Int?
  parent_id           Int
  path                String              @db.VarChar
  users_id            Int?
  payload_preferences payload_preferences @relation(fields: [parent_id], references: [id], onDelete: Cascade, onUpdate: NoAction, map: "payload_preferences_rels_parent_fk")
  users               users?              @relation(fields: [users_id], references: [id], onDelete: Cascade, onUpdate: NoAction, map: "payload_preferences_rels_users_fk")

  @@index([order])
  @@index([parent_id], map: "payload_preferences_rels_parent_idx")
  @@index([path])
  @@index([users_id])
}

model products {
  id                            Int                             @id @default(autoincrement())
  title                         String                          @db.VarChar
  caption                       String?                         @default("") @db.VarChar
  date                          DateTime?                       @default(dbgenerated("'2024-01-01 00:00:00+00'::timestamp with time zone")) @db.Timestamptz(3)
  price                         Decimal                         @default(0) @db.Decimal
  image_id                      Int?
  updated_at                    DateTime                        @default(now()) @db.Timestamptz(3)
  created_at                    DateTime                        @default(now()) @db.Timestamptz(3)
  payload_locked_documents_rels payload_locked_documents_rels[]
  media                         media?                          @relation(fields: [image_id], references: [id], onUpdate: NoAction, map: "products_image_id_media_id_fk")
  products_details              products_details[]
  products_features             products_features[]
  products_gallery              products_gallery[]
  products_rels                 products_rels[]

  @@index([created_at])
  @@index([image_id], map: "products_image_idx")
  @@index([updated_at])
}

model products_details {
  order       Int      @map("_order")
  parent_id   Int      @map("_parent_id")
  id          String   @id @db.VarChar
  title       String?  @db.VarChar
  description String?  @db.VarChar
  products    products @relation(fields: [parent_id], references: [id], onDelete: Cascade, onUpdate: NoAction, map: "products_details_parent_id_fk")

  @@index([order], map: "products_details_order_idx")
  @@index([parent_id], map: "products_details_parent_id_idx")
}

model products_features {
  order     Int      @map("_order")
  parent_id Int      @map("_parent_id")
  id        String   @id @db.VarChar
  name      String?  @db.VarChar
  products  products @relation(fields: [parent_id], references: [id], onDelete: Cascade, onUpdate: NoAction, map: "products_features_parent_id_fk")

  @@index([order], map: "products_features_order_idx")
  @@index([parent_id], map: "products_features_parent_id_idx")
}

model products_gallery {
  order      Int      @map("_order")
  parent_id  Int      @map("_parent_id")
  id         String   @id @db.VarChar
  picture_id Int?
  products   products @relation(fields: [parent_id], references: [id], onDelete: Cascade, onUpdate: NoAction, map: "products_gallery_parent_id_fk")
  media      media?   @relation(fields: [picture_id], references: [id], onUpdate: NoAction, map: "products_gallery_picture_id_media_id_fk")

  @@index([order], map: "products_gallery_order_idx")
  @@index([parent_id], map: "products_gallery_parent_id_idx")
  @@index([picture_id], map: "products_gallery_picture_idx")
}

model products_rels {
  id            Int         @id @default(autoincrement())
  order         Int?
  parent_id     Int
  path          String      @db.VarChar
  categories_id Int?
  categories    categories? @relation(fields: [categories_id], references: [id], onDelete: Cascade, onUpdate: NoAction, map: "products_rels_categories_fk")
  products      products    @relation(fields: [parent_id], references: [id], onDelete: Cascade, onUpdate: NoAction, map: "products_rels_parent_fk")

  @@index([categories_id])
  @@index([order])
  @@index([parent_id], map: "products_rels_parent_idx")
  @@index([path])
}

model projects {
  id                            Int                             @id @default(autoincrement())
  title                         String                          @db.VarChar
  caption                       String?                         @db.VarChar
  client                        String?                         @db.VarChar
  type                          enum_projects_type?
  date                          DateTime?                       @default(dbgenerated("'2024-01-01 00:00:00+00'::timestamp with time zone")) @db.Timestamptz(3)
  description                   String                          @db.VarChar
  image_id                      Int?
  updated_at                    DateTime                        @default(now()) @db.Timestamptz(3)
  created_at                    DateTime                        @default(now()) @db.Timestamptz(3)
  clients_rels                  clients_rels[]
  payload_locked_documents_rels payload_locked_documents_rels[]
  media                         media?                          @relation(fields: [image_id], references: [id], onUpdate: NoAction, map: "projects_image_id_media_id_fk")
  projects_gallery              projects_gallery[]
  projects_links                projects_links[]
  projects_rels                 projects_rels[]

  @@index([created_at])
  @@index([image_id], map: "projects_image_idx")
  @@index([updated_at])
}

model projects_gallery {
  order      Int      @map("_order")
  parent_id  Int      @map("_parent_id")
  id         String   @id @db.VarChar
  label      String?  @db.VarChar
  picture_id Int?
  projects   projects @relation(fields: [parent_id], references: [id], onDelete: Cascade, onUpdate: NoAction, map: "projects_gallery_parent_id_fk")
  media      media?   @relation(fields: [picture_id], references: [id], onUpdate: NoAction, map: "projects_gallery_picture_id_media_id_fk")

  @@index([order], map: "projects_gallery_order_idx")
  @@index([parent_id], map: "projects_gallery_parent_id_idx")
  @@index([picture_id], map: "projects_gallery_picture_idx")
}

model projects_links {
  order     Int      @map("_order")
  parent_id Int      @map("_parent_id")
  id        String   @id @db.VarChar
  label     String?  @db.VarChar
  url       String?  @db.VarChar
  projects  projects @relation(fields: [parent_id], references: [id], onDelete: Cascade, onUpdate: NoAction, map: "projects_links_parent_id_fk")

  @@index([order], map: "projects_links_order_idx")
  @@index([parent_id], map: "projects_links_parent_id_idx")
}

model projects_rels {
  id            Int         @id @default(autoincrement())
  order         Int?
  parent_id     Int
  path          String      @db.VarChar
  categories_id Int?
  categories    categories? @relation(fields: [categories_id], references: [id], onDelete: Cascade, onUpdate: NoAction, map: "projects_rels_categories_fk")
  projects      projects    @relation(fields: [parent_id], references: [id], onDelete: Cascade, onUpdate: NoAction, map: "projects_rels_parent_fk")

  @@index([categories_id])
  @@index([order])
  @@index([parent_id], map: "projects_rels_parent_idx")
  @@index([path])
}

model users {
  id                            Int                             @id @default(autoincrement())
  updated_at                    DateTime                        @default(now()) @db.Timestamptz(3)
  created_at                    DateTime                        @default(now()) @db.Timestamptz(3)
  email                         String                          @unique(map: "users_email_idx") @db.VarChar
  reset_password_token          String?                         @db.VarChar
  reset_password_expiration     DateTime?                       @db.Timestamptz(3)
  salt                          String?                         @db.VarChar
  hash                          String?                         @db.VarChar
  login_attempts                Decimal?                        @default(0) @db.Decimal
  lock_until                    DateTime?                       @db.Timestamptz(3)
  payload_locked_documents_rels payload_locked_documents_rels[]
  payload_preferences_rels      payload_preferences_rels[]
  users_sessions                users_sessions[]

  @@index([created_at])
  @@index([updated_at])
}

model users_sessions {
  order      Int       @map("_order")
  parent_id  Int       @map("_parent_id")
  id         String    @id @db.VarChar
  created_at DateTime? @db.Timestamptz(3)
  expires_at DateTime  @db.Timestamptz(3)
  users      users     @relation(fields: [parent_id], references: [id], onDelete: Cascade, onUpdate: NoAction, map: "users_sessions_parent_id_fk")

  @@index([order], map: "users_sessions_order_idx")
  @@index([parent_id], map: "users_sessions_parent_id_idx")
}

enum enum_clients_interest {
  wishlist
  newsletter
}

enum enum_projects_type {
  project
  saas
}

// NextAuth Models
model user {
  id String @id @unique @default(cuid())

  name          String?
  email         String          @unique
  emailVerified DateTime?
  image         String?
  accounts      account[]
  sessions      session[]
  // Optional for WebAuthn support
  authenticator authenticator[]

  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  profile profile[]

  role   role?   @relation(fields: [roleId], references: [id])
  roleId String?
}

model profile {
  id String @id @unique @default(cuid())

  about   String   @db.Text
  website String
  socials String[]

  location String

  user   user   @relation(fields: [userId], references: [id], onDelete: Cascade)
  userId String

  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt
}

model account {
  id String @id @unique @default(cuid())

  password String?

  type              String
  provider          String
  providerAccountId String
  refresh_token     String?
  access_token      String?
  expires_at        Int?
  token_type        String?
  scope             String?
  id_token          String?
  session_state     String?

  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  user   user   @relation(fields: [userId], references: [id], onDelete: Cascade)
  userId String

  proposal proposal[]
  contract contract[]
  member   member[]
}

model session {
  sessionToken String   @unique
  userId       String
  expires      DateTime
  user         user     @relation(fields: [userId], references: [id], onDelete: Cascade)

  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt
}

model verificationToken {
  identifier String
  token      String
  expires    DateTime

  @@id([identifier, token])
}

// Optional for WebAuthn support
model authenticator {
  credentialID         String  @unique
  userId               String
  providerAccountId    String
  credentialPublicKey  String
  counter              Int
  credentialDeviceType String
  credentialBackedUp   Boolean
  transports           String?

  user user @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@id([userId, credentialID])
}

// Manager models
model proposal {
  id String @id @default(cuid())

  name        String
  description String? @db.Text

  status status @default(created)

  links       String[]
  attachments document[]

  milestones   Json[]
  fixed_budget Float

  total_budget Float

  duration                       Int
  agreed_to_terms_and_conditions Boolean

  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  account   account @relation(fields: [accountId], references: [id])
  accountId String

  contract contract[]
}

model document {
  id String @id @default(cuid())

  name String
  path String @unique

  file_type String
  size      String

  status status @default(created)

  category String

  association_entity String
  association_id     String

  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  proposal   proposal? @relation(fields: [proposalId], references: [id])
  proposalId String?

  contract   contract? @relation(fields: [contractId], references: [id])
  contractId String?
  message    message?  @relation(fields: [messageId], references: [id])
  messageId  String?
}

model contract {
  id String @id @default(cuid())

  name        String
  description String? @db.Text

  status status @default(created)

  agreed_to_terms_and_conditions Boolean @default(false)

  value Float

  start_date DateTime
  end_date   DateTime

  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  client   account @relation(fields: [clientId], references: [id])
  clientId String

  proposal   proposal? @relation(fields: [proposalId], references: [id])
  proposalId String?

  document document[]
  room     room[]
}

model member {
  id String @id @default(cuid())

  account   account @relation(fields: [accountId], references: [id])
  accountId String

  room   room   @relation(fields: [roomId], references: [id])
  roomId String
}

model room {
  id String @id @default(cuid())

  name  String
  about String? @db.Text

  contract   contract? @relation(fields: [contractId], references: [id])
  contractId String?

  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  messages message[]
  members  member[]
}

model message {
  id String @id @default(cuid())

  content String

  attachments  document[]
  associations Json[]

  sent_from String
  sent_to   String

  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  room   room?   @relation(fields: [roomId], references: [id])
  roomId String?
}

model role {
  id String @id @default(cuid())

  name        String?
  description String?

  status status @default(created)

  createdAt DateTime  @default(now())
  updatedAt DateTime?

  permissions Json

  user user[]
}

enum status {
  active
  inactive

  submitted
  received
  negotiating
  agreed

  created
  inprogress
  reviewing
  completed
  closed
  terminated
}
